env/
__pycache__/
.DS_Store
*.csv
eval_results/
eval_data/
*.egg-info/
results/
.env
tradingagents/dataflows/data_cache/
CLAUDE.md
.claude/
.taskmaster/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 
