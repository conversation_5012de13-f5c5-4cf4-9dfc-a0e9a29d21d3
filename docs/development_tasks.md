# Danh sách Công việc Phát triển (Development Tasks)

Dựa trên Product Backlog, đây là danh sách các công việc được chia nhỏ cho đội ngũ phát triển.

**Quy ước trạng thái:**
- `[ ]` - **To Do**: Task chưa được bắt đầu.
- `[/]` - **In Progress**: Task đang được thực hiện.
- `[x]` - **Done**: Task đã hoàn thành.

---

### Epic 1: Phân tích và Cảnh báo Rủi ro Vĩ mô Việt Nam

**US1.1: L<PERSON> một nhà đầu tư, tôi muốn xem báo cáo tóm tắt về tình hình kinh tế vĩ mô Việt Nam để hiểu bối cảnh chung của thị trường.**

- [ ] **Task 1.1.1:** [Backend] X<PERSON><PERSON> đ<PERSON> và tích hợp API nguồn dữ liệu vĩ mô Việt Nam (GDP, lạm ph<PERSON>t, lã<PERSON> su<PERSON>, v.v.).
- [ ] **Task 1.1.2:** [Backend] Thiết kế schema/model để lưu trữ và quản lý dữ liệu vĩ mô.
- [ ] **Task 1.1.3:** [Backend] Xây dựng API endpoint (`/api/macro-economic/summary`) để cung cấp báo cáo tóm tắt các chỉ số vĩ mô.
- [ ] **Task 1.1.4:** [Frontend] Thiết kế và triển khai giao diện người dùng để hiển thị báo cáo tóm tắt tình hình kinh tế vĩ mô.

**US1.2: Là một nhà đầu tư, tôi muốn biết tác động tiềm năng của các yếu tố vĩ mô lên cổ phiếu tôi đang quan tâm để đánh giá rủi ro hệ thống.**

- [ ] **Task 1.2.1:** [Backend] Xây dựng logic để phân loại cổ phiếu theo ngành và xác định mối liên hệ với các chỉ số vĩ mô.
- [ ] **Task 1.2.2:** [Backend] Mở rộng API endpoint (`/api/stocks/{ticker}/macro-impact`) để trả về phân tích tác động vĩ mô và mức độ rủi ro.
- [ ] **Task 1.2.3:** [Frontend] Tích hợp và hiển thị phân tích tác động vĩ mô trên trang chi tiết cổ phiếu.
- [ ] **Task 1.2.4:** [Frontend] Sử dụng biểu đồ hoặc các yếu tố trực quan để minh họa mối liên hệ và mức độ rủi ro.

---

### Epic 2: Sàng lọc Cổ phiếu "An toàn" theo Chuẩn Mực

**US2.2: Là một nhà đầu tư, tôi muốn xem các tiêu chí cụ thể mà hệ thống dùng để đánh giá "Điểm An toàn Tài chính" để hiểu rõ hơn về phân tích.**

- [ ] **Task 2.2.1:** [Backend] Cập nhật API tính "Điểm An toàn Tài chính" để trả về danh sách các tiêu chí, giá trị thực tế của cổ phiếu, và trạng thái "Đạt"/"Không đạt".
- [ ] **Task 2.2.2:** [Frontend] Thiết kế và triển khai một thành phần UI (ví dụ: modal hoặc section có thể mở rộng) để hiển thị chi tiết các tiêu chí đánh giá.
- [ ] **Task 2.2.3:** [Frontend] Thêm giải thích ngắn gọn (tooltip) cho từng tiêu chí.

---

### Epic 3: Cảnh báo Rủi ro Thao túng giá (Dấu hiệu "Đội Lái")

**US3.1: Là một nhà đầu tư, tôi muốn nhận được cảnh báo về nguy cơ thao túng giá của một cổ phiếu để tránh các cạm bẫy.**

- [ ] **Task 3.1.1:** [Backend] Nghiên cứu và triển khai thuật toán phát hiện bất thường về khối lượng và giá giao dịch.
- [ ] **Task 3.1.2:** [Backend] Xây dựng API endpoint (`/api/stocks/{ticker}/manipulation-risk`) để trả về mức độ rủi ro thao túng.
- [ ] **Task 3.1.3:** [Frontend] Hiển thị cảnh báo rủi ro thao túng một cách nổi bật trên trang chi tiết cổ phiếu.

**US3.2: Là một nhà đầu tư, tôi muốn xem các dấu hiệu cụ thể mà hệ thống dùng để cảnh báo thao túng giá để tôi có thể tự đánh giá và học hỏi.**

- [ ] **Task 3.2.1:** [Backend] Cập nhật API từ Task 3.1.2 để trả về danh sách các dấu hiệu cụ thể đã được phát hiện.
- [ ] **Task 3.2.2:** [Frontend] Triển khai UI để liệt kê các dấu hiệu thao túng giá mà hệ thống đã phát hiện.

---

### Epic 4: Phân tích và Cảnh báo Rủi ro T+2.5

**US4.1: Là một nhà đầu tư, tôi muốn biết mức độ rủi ro biến động giá của cổ phiếu trong giai đoạn T+2.5 để đưa ra quyết định mua/bán phù hợp.**

- [ ] **Task 4.1.1:** [Backend] Triển khai logic tính toán độ biến động lịch sử (historical volatility) của cổ phiếu.
- [ ] **Task 4.1.2:** [Backend] Xây dựng API endpoint (`/api/stocks/{ticker}/t25-risk`) để trả về mức độ rủi ro biến động giá T+2.5.
- [ ] **Task 4.1.3:** [Frontend] Hiển thị cảnh báo rủi ro T+2.5 trên trang chi tiết cổ phiếu.

**US4.2: Là một nhà đầu tư, tôi muốn xem dự báo biến động giá tiềm năng trong giai đoạn T+2.5 để có cái nhìn cụ thể hơn về rủi ro.**

- [ ] **Task 4.2.1:** [Backend] Cập nhật API từ Task 4.1.2 để trả về biên độ biến động giá dự báo (ví dụ: +/- X%).
- [ ] **Task 4.2.2:** [Frontend] Hiển thị biên độ biến động giá dự báo một cách dễ hiểu bên cạnh cảnh báo rủi ro T+2.5.

---

### Epic 5: Giao diện Người dùng Đơn giản và Trực quan

**US5.3: Là một nhà đầu tư, tôi muốn có khả năng xem lại các cổ phiếu đã phân tích gần đây để tiết kiệm thời gian nhập lại.**

- [ ] **Task 5.3.1:** [Frontend] Sử dụng Local Storage hoặc state management để lưu lại lịch sử các mã cổ phiếu đã tìm kiếm.
- [ ] **Task 5.3.2:** [Frontend] Thiết kế và triển khai UI để hiển thị danh sách lịch sử tìm kiếm.
- [ ] **Task 5.3.3:** [Frontend] Triển khai chức năng cho phép người dùng nhấp vào một mã trong lịch sử để tra cứu lại.
