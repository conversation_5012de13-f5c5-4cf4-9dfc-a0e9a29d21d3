# Danh sách Công việc <PERSON>riể<PERSON> (Development Tasks)

Dựa trên Product Backlog, đây là danh sách các công việc được chia nhỏ cho đội ngũ phát triển.

**Quy ước trạng thái:**
- `[ ]` - **To Do**: Task chưa được bắt đầu.
- `[/]` - **In Progress**: Task đang được thực hiện.
- `[x]` - **Done**: Task đã hoàn thành.

---

### Epic 6: Tích hợp Cơ sở dữ liệu

**US6.1: <PERSON><PERSON> một nhà phát triển, tôi muốn hệ thống có thể kết nối và lưu trữ dữ liệu trong cơ sở dữ liệu MySQL để đảm bảo tính toàn vẹn và khả năng truy vấn.**

- [x] **Task 6.1.1:** [System] <PERSON><PERSON> sung `SQLAlchemy`, `<PERSON><PERSON>`, `pymysql` vào `requirements.txt`.
- [x] **Task 6.1.2:** [Config] Thêm cấu hình `DATABASE_URL` vào `tradingagents/default_config.py`.
- [ ] **Task 6.1.3:** [System] Chạy `pip install -r requirements.txt` để cài đặt các thư viện mới.
- [ ] **Task 6.1.4:** [Backend] Khởi tạo cấu trúc Alembic để quản lý database migrations.
- [ ] **Task 6.1.5:** [Backend] Thiết kế và tạo migration cho các bảng dữ liệu ban đầu (ví dụ: `macro_economic_data`, `stock_analysis_results`).

---

### Epic 1: Phân tích và Cảnh báo Rủi ro Vĩ mô Việt Nam

**US1.1: Là một nhà đầu tư, tôi muốn xem báo cáo tóm tắt về tình hình kinh tế vĩ mô Việt Nam, và có thể làm mới dữ liệu khi cần.**

- [ ] **Task 1.1.1:** [Backend] Thiết kế schema/model cho bảng `macro_economic_data`, bao gồm các trường dữ liệu và một trường `last_updated` (timestamp).
- [ ] **Task 1.1.2:** [Backend] Tạo migration cho bảng `macro_economic_data`.
- [ ] **Task 1.1.3:** [Backend] Sửa đổi API endpoint (`/api/macro-economic/summary`) để:
    - Chấp nhận một tham số tùy chọn, ví dụ: `force_refresh=true`.
    - **Logic chính:**
        1. Nếu `force_refresh=true` hoặc bảng `macro_economic_data` rỗng, gọi API ngoài để lấy dữ liệu mới.
        2. Xóa dữ liệu cũ (nếu có) và ghi dữ liệu mới vào bảng, cập nhật `last_updated`.
        3. Nếu `force_refresh=false` (mặc định) và trong bảng đã có dữ liệu, lấy trực tiếp từ DB.
    - API response phải bao gồm cả dữ liệu vĩ mô và ngày cập nhật (`last_updated`).
- [ ] **Task 1.1.4:** [Frontend] Trên giao diện hiển thị báo cáo vĩ mô:
    - Hiển thị ngày cập nhật dữ liệu (lấy từ API response).
    - Thêm một nút "Làm mới dữ liệu" (Refresh) để người dùng có thể chủ động gọi lại API với tham số `force_refresh=true`.

**US1.2: Là một nhà đầu tư, tôi muốn biết tác động tiềm năng của các yếu tố vĩ mô lên cổ phiếu tôi đang quan tâm để đánh giá rủi ro hệ thống.**

- [ ] **Task 1.2.1:** [Backend] Thiết kế schema/model và tạo migration cho bảng `stock_macro_analysis` để lưu kết quả phân tích vĩ mô theo từng cổ phiếu.
- [ ] **Task 1.2.2:** [Backend] Xây dựng logic để phân loại cổ phiếu theo ngành và xác định mối liên hệ với các chỉ số vĩ mô.
- [ ] **Task 1.2.3:** [Backend] Cập nhật API endpoint (`/api/stocks/{ticker}/macro-impact`) để kiểm tra và trả về kết quả từ DB trước, nếu không có thì mới phân tích, lưu kết quả rồi trả về.
- [ ] **Task 1.2.4:** [Frontend] Tích hợp và hiển thị phân tích tác động vĩ mô trên trang chi tiết cổ phiếu.
- [ ] **Task 1.2.5:** [Frontend] Sử dụng biểu đồ hoặc các yếu tố trực quan để minh họa mối liên hệ và mức độ rủi ro.

---

### Epic 2: Sàng lọc Cổ phiếu "An toàn" theo Chuẩn Mực

**US2.2: Là một nhà đầu tư, tôi muốn xem các tiêu chí cụ thể mà hệ thống dùng để đánh giá "Điểm An toàn Tài chính" để hiểu rõ hơn về phân tích.**
- [ ] **Task 2.2.1:** [Backend] Thiết kế schema/model và tạo migration cho bảng `stock_safety_analysis` để lưu điểm và các tiêu chí an toàn.
- [ ] **Task 2.2.2:** [Backend] Cập nhật API tính "Điểm An toàn Tài chính" để ưu tiên lấy dữ liệu từ DB, nếu chưa có thì phân tích, lưu lại rồi trả về.
- [ ] **Task 2.2.3:** [Frontend] Thiết kế và triển khai một thành phần UI (ví dụ: modal hoặc section có thể mở rộng) để hiển thị chi tiết các tiêu chí đánh giá.
- [ ] **Task 2.2.4:** [Frontend] Thêm giải thích ngắn gọn (tooltip) cho từng tiêu chí.

---

### Epic 3: Cảnh báo Rủi ro Thao túng giá (Dấu hiệu "Đội Lái")

**US3.1 & US3.2: ...tôi muốn nhận được cảnh báo về nguy cơ thao túng giá và xem các dấu hiệu cụ thể...**
- [ ] **Task 3.1.1:** [Backend] Thiết kế schema/model và tạo migration cho bảng `stock_manipulation_risk` để lưu kết quả phân tích.
- [ ] **Task 3.1.2:** [Backend] Nghiên cứu và triển khai thuật toán phát hiện bất thường về khối lượng và giá giao dịch.
- [ ] **Task 3.1.3:** [Backend] Xây dựng API endpoint (`/api/stocks/{ticker}/manipulation-risk`) ưu tiên đọc từ DB, nếu chưa có thì phân tích, lưu lại rồi trả về.
- [ ] **Task 3.1.4:** [Frontend] Hiển thị cảnh báo rủi ro thao túng và các dấu hiệu cụ thể một cách nổi bật trên trang chi tiết cổ phiếu.

---

### Epic 4: Phân tích và Cảnh báo Rủi ro T+2.5

**US4.1 & US4.2: ...tôi muốn biết mức độ rủi ro biến động giá và xem dự báo tiềm năng trong giai đoạn T+2.5...**
- [ ] **Task 4.1.1:** [Backend] Thiết kế schema/model và tạo migration cho bảng `stock_t25_risk` để lưu kết quả phân tích.
- [ ] **Task 4.1.2:** [Backend] Triển khai logic tính toán độ biến động lịch sử (historical volatility) của cổ phiếu.
- [ ] **Task 4.1.3:** [Backend] Xây dựng API endpoint (`/api/stocks/{ticker}/t25-risk`) ưu tiên đọc từ DB, nếu chưa có thì phân tích, lưu lại rồi trả về.
- [ ] **Task 4.1.4:** [Frontend] Hiển thị cảnh báo rủi ro T+2.5 và biên độ biến động dự báo trên trang chi tiết cổ phiếu.

---

### Epic 5: Giao diện Người dùng Đơn giản và Trực quan

**US5.3: Là một nhà đầu tư, tôi muốn có khả năng xem lại các cổ phiếu đã phân tích gần đây để tiết kiệm thời gian nhập lại.**

- [ ] **Task 5.3.1:** [Frontend] Sử dụng Local Storage hoặc state management để lưu lại lịch sử các mã cổ phiếu đã tìm kiếm.
- [ ] **Task 5.3.2:** [Frontend] Thiết kế và triển khai UI để hiển thị danh sách lịch sử tìm kiếm.
- [ ] **Task 5.3.3:** [Frontend] Triển khai chức năng cho phép người dùng nhấp vào một mã trong lịch sử để tra cứu lại.
