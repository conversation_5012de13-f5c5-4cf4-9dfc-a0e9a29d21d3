# Hướng dẫn API Dữ liệu Kinh tế Vĩ mô

## Tổng quan

API Dữ liệu Kinh tế Vĩ mô cung cấp khả năng truy cập các chỉ số kinh tế quan trọng của Việt Nam và các quốc gia khác thông qua World Bank API. API này hỗ trợ các agents phân tích tác động của các yếu tố vĩ mô lên thị trường chứng khoán.

## Cấu hình

### Biến môi trường

```bash
# Bật/tắt API vĩ mô (mặc định: true)
USE_MACRO_API=true

# URL cơ sở của API (mặc định: World Bank API)
MACRO_API_BASE_URL=http://api.worldbank.org/v2

# API key (World Bank không yêu cầu)
MACRO_API_KEY=

# Timeout cho requests (giây)
API_TIMEOUT=30
```

### Cấu hình trong code

```python
from tradingagents.dataflows.config import set_config

config = {
    "use_macro_api": True,
    "macro_api_base_url": "http://api.worldbank.org/v2",
    "api_timeout": 30
}
set_config(config)
```

## Các chỉ số được hỗ trợ

| Chỉ số | Mã World Bank | Mô tả |
|--------|---------------|-------|
| GDP | NY.GDP.MKTP.CD | Tổng sản phẩm quốc nội (USD hiện tại) |
| Tăng trưởng GDP | NY.GDP.MKTP.KD.ZG | Tăng trưởng GDP (% hàng năm) |
| Lạm phát | FP.CPI.TOTL.ZG | Lạm phát, giá tiêu dùng (% hàng năm) |
| Lãi suất | FR.INR.RINR | Lãi suất thực (%) |
| Thất nghiệp | SL.UEM.TOTL.ZS | Tỷ lệ thất nghiệp (% tổng lực lượng lao động) |
| Xuất khẩu | NE.EXP.GNFS.CD | Xuất khẩu hàng hóa và dịch vụ (USD hiện tại) |
| Nhập khẩu | NE.IMP.GNFS.CD | Nhập khẩu hàng hóa và dịch vụ (USD hiện tại) |
| FDI | BX.KLT.DINV.CD.WD | Đầu tư trực tiếp nước ngoài, dòng vào ròng |
| Cán cân thanh toán | BN.CAB.XOKA.CD | Cán cân tài khoản vãng lai |
| Nợ chính phủ | GC.DOD.TOTL.GD.ZS | Nợ chính phủ trung ương (% GDP) |

## API Functions

### 1. get_macro_economic_summary()

Lấy báo cáo tóm tắt kinh tế vĩ mô toàn diện.

```python
from tradingagents.dataflows.interface import get_macro_economic_summary

# Lấy báo cáo 5 năm gần nhất cho Việt Nam
result = get_macro_economic_summary('VN', '2024-12-01', 5)
print(result)
```

**Tham số:**
- `country_code` (str): Mã quốc gia ISO (mặc định: 'VN')
- `curr_date` (str): Ngày hiện tại định dạng yyyy-mm-dd
- `years_back` (int): Số năm nhìn lại (mặc định: 5)

### 2. get_gdp_trend()

Phân tích xu hướng GDP.

```python
from tradingagents.dataflows.interface import get_gdp_trend

# Lấy xu hướng GDP 10 năm
result = get_gdp_trend('VN', 10)
print(result)
```

**Tham số:**
- `country_code` (str): Mã quốc gia ISO (mặc định: 'VN')
- `years_back` (int): Số năm nhìn lại (mặc định: 10)

### 3. get_inflation_trend()

Phân tích xu hướng lạm phát.

```python
from tradingagents.dataflows.interface import get_inflation_trend

# Lấy xu hướng lạm phát 10 năm
result = get_inflation_trend('VN', 10)
print(result)
```

### 4. get_interest_rate_trend()

Phân tích xu hướng lãi suất.

```python
from tradingagents.dataflows.interface import get_interest_rate_trend

# Lấy xu hướng lãi suất 10 năm
result = get_interest_rate_trend('VN', 10)
print(result)
```

## Sử dụng trực tiếp MacroEconomicAPI

```python
from tradingagents.dataflows.macro_economic_api import get_macro_api

# Lấy instance API
macro_api = get_macro_api()

# Lấy dữ liệu GDP
gdp_data = macro_api.get_gdp_data('VN', 2020, 2024)

# Lấy báo cáo toàn diện
report = macro_api.get_comprehensive_macro_data('VN', 2020, 2024)
print(report)
```

## Định dạng dữ liệu trả về

API trả về dữ liệu dưới dạng Markdown được format sẵn, bao gồm:

- Bảng dữ liệu theo năm
- Phân tích xu hướng (tăng/giảm/ổn định)
- Thống kê tóm tắt
- Giá trị mới nhất và thay đổi so với năm trước

## Xử lý lỗi

```python
try:
    result = get_macro_economic_summary('VN')
    if "Lỗi" in result or "Không có dữ liệu" in result:
        print("Có lỗi xảy ra hoặc không có dữ liệu")
    else:
        print("Dữ liệu thành công:", result)
except Exception as e:
    print(f"Exception: {e}")
```

## Mã quốc gia phổ biến

| Quốc gia | Mã ISO |
|----------|--------|
| Việt Nam | VN |
| Hoa Kỳ | US |
| Trung Quốc | CN |
| Nhật Bản | JP |
| Hàn Quốc | KR |
| Thái Lan | TH |
| Singapore | SG |
| Malaysia | MY |
| Indonesia | ID |
| Philippines | PH |

## Ví dụ kết quả

```markdown
# Báo cáo Kinh tế Vĩ mô - Việt Nam
Giai đoạn: 2019 - 2023

## Tổng sản phẩm quốc nội (GDP)

| Năm | Giá trị |
|-----|--------|
| 2023 | $433,857,681,378 |
| 2022 | $413,445,230,669 |
| 2021 | $366,474,752,000 |

### Phân tích xu hướng
- Giá trị mới nhất (2023): 433,857,681,378.00 USD
- Thay đổi so với năm trước: +20,412,450,709.00 USD (+4.94%)
- Xu hướng: Tăng
```

## Lưu ý

1. **Độ trễ dữ liệu**: Dữ liệu World Bank thường có độ trễ 1-2 năm
2. **Giới hạn rate**: World Bank API có giới hạn requests, nhưng khá cao cho sử dụng thông thường
3. **Kết nối mạng**: API yêu cầu kết nối internet để truy cập World Bank
4. **Cache**: Nên implement cache cho dữ liệu ít thay đổi để tối ưu hiệu suất

## Troubleshooting

### Lỗi kết nối
```
Error fetching GDP data for VN: HTTPSConnectionPool...
```
**Giải pháp**: Kiểm tra kết nối internet và firewall

### Không có dữ liệu
```
Không có dữ liệu GDP khả dụng.
```
**Giải pháp**: Thử với mã quốc gia khác hoặc khoảng thời gian khác

### API bị tắt
```
Macroeconomic API is disabled in configuration.
```
**Giải pháp**: Đặt `USE_MACRO_API=true` trong environment variables
