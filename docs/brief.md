# Project Brief: TradingAgents (<PERSON><PERSON>n b<PERSON><PERSON>)

## Executive Summary

**TradingAgents (Phiên bản Việt Nam)** là một nền tảng hỗ trợ quyết định đầu tư, đ<PERSON><PERSON><PERSON> thiết kế đặc biệt cho **các nhà đầu tư cá nhân và nhà nghiên cứu tại thị trường chứng khoán Việt Nam**. Sử dụng hệ thống đa tác nhân (multi-agent) dựa trên AI, dự án tập trung vào việc **giảm thiểu rủi ro** bằng cách tự động hóa quy trình phân tích sâu và cung cấp các góc nhìn đa chiều. Nền tảng sẽ mô phỏng một đội ngũ chuyên gia tài chính ảo, bao gồm các agent chuyên phân tích dữ liệu vĩ mô <PERSON>, b<PERSON><PERSON><PERSON><PERSON>, tin tức thị trườ<PERSON>, và các yếu tố đặc thù của thị trường trong nước để đưa ra các khuyến nghị đầu tư an toàn và thận trọng.

*   **Vấn đề chính cần giải quyết:** Giúp nhà đầu tư cá nhân tại Việt Nam vượt qua sự phức tạp và bất cân xứng thông tin của thị trường, đồng thời cung cấp các công cụ để xây dựng chiến lược đầu tư tập trung vào việc bảo toàn vốn và giảm thiểu rủi ro.
*   **Đối tượng mục tiêu:** Nhà đầu tư cá nhân và nhà nghiên cứu không chuyên tại thị trường chứng khoán Việt Nam, những người ưu tiên sự an toàn và cần một công cụ hỗ trợ đáng tin cậy.
*   **Giá trị cốt lõi:** Dân chủ hóa khả năng phân tích tài chính chuyên sâu, cung cấp các chiến lược giảm thiểu rủi ro đã được kiểm chứng, và trang bị cho nhà đầu tư cá nhân những hiểu biết sâu sắc để đưa ra quyết định tự tin và an toàn hơn.

---

## Problem Statement

Các nhà đầu tư cá nhân tại thị trường chứng khoán Việt Nam phải đối mặt với một môi trường đầy biến động và phức tạp. Họ thường thiếu các công cụ phân tích chuyên sâu và khách quan để đưa ra quyết định một cách tự tin.

*   **Thực trạng và Nỗi đau (Current State and Pain Points):**
    *   **Sự bất cân xứng thông tin:** Nhà đầu tư nhỏ lẻ thường tiếp cận thông tin chậm hơn và phải đối mặt với rủi ro từ tin đồn, tin tức giả, và các hoạt động "lái" giá tinh vi.
    *   **Quá tải dữ liệu:** Việc tự mình phân tích báo cáo tài chính dày đặc, tin tức vĩ mô, và các chỉ số kỹ thuật là một thách thức lớn, đòi hỏi nhiều thời gian và chuyên môn.
    *   **Rủi ro đặc thù của thị trường:** Các yếu tố như chu kỳ thanh toán T+2.5 tạo ra một khoảng thời gian rủi ro mà các công cụ phân tích tiêu chuẩn thường bỏ qua.
    *   **Ra quyết định theo cảm tính:** Thiếu một quy trình phân tích có hệ thống, nhiều nhà đầu tư dễ bị ảnh hưởng bởi tâm lý đám đông (FOMO), dẫn đến các quyết định mua bán vội vàng và thua lỗ.

*   **Tác động của vấn đề:** Tỷ lệ thua lỗ ở các nhà đầu tư mới là rất cao, gây mất niềm tin vào thị trường và cản trở việc xây dựng một chiến lược đầu tư dài hạn, bền vững.

*   **Tại sao các giải pháp hiện tại chưa đủ tốt:**
    *   Các báo cáo từ công ty chứng khoán có thể hữu ích nhưng đôi khi mang tính chủ quan hoặc quá phức tạp.
    *   Các công cụ sàng lọc cổ phiếu hiện có thường chỉ dựa trên dữ liệu tài chính cơ bản, thiếu khả năng phân tích sâu về chất lượng doanh nghiệp, rủi ro thị trường, hay các yếutố đặc thù của Việt Nam.
    *   Các diễn đàn và mạng xã hội là nguồn thông tin nhiễu loạn, khó kiểm chứng.

---

## Proposed Solution

Chúng tôi đề xuất xây dựng **TradingAgents (Phiên bản Việt Nam)**, một nền tảng hỗ trợ quyết định đầu tư thông minh, tập trung vào việc **phân tích rủi ro và sàng lọc cơ hội an toàn** dành riêng cho thị trường chứng khoán Việt Nam. Nền tảng sẽ hoạt động như một "đội ngũ chuyên gia tài chính ảo", tự động hóa các quy trình phân tích phức tạp và cung cấp cho người dùng những khuyến nghị rõ ràng, dễ hiểu và được cá nhân hóa.

*   **Khái niệm và Cách tiếp cận cốt lõi:**
    Thay vì chỉ cung cấp dữ liệu thô, nền tảng sẽ sử dụng một hệ thống đa tác nhân (multi-agent) để **tổng hợp và diễn giải thông tin**. Mỗi agent sẽ có một chuyên môn riêng, cộng tác với nhau để tạo ra một bức tranh toàn cảnh và đáng tin cậy.

*   **Các thành phần chính của giải pháp (Các tính năng sáng tạo):**
    1.  **Agent Phân tích Vĩ mô & Chính sách Việt Nam:** Một agent chuyên theo dõi và phân tích các yếu tố kinh tế vĩ mô (lãi suất, lạm phát, GDP) và chính sách từ các nguồn uy tín, giúp người dùng hiểu được "bức tranh lớn" và các rủi ro hệ thống.
    2.  **Agent Cảnh báo Rủi ro Thao túng giá:** Sử dụng AI để phát hiện các dấu hiệu bất thường trong khối lượng giao dịch, biến động giá, và tin tức liên quan có thể là dấu hiệu của hoạt động "lái" giá. Agent sẽ đưa ra một "điểm rủi ro thao túng" cho cổ phiếu.
    3.  **Bộ lọc Đầu tư An toàn theo Chuẩn Mực:** Cung cấp các bộ lọc được cấu hình sẵn để sàng lọc cổ phiếu dựa trên các tiêu chí đầu tư giá trị an toàn (ví dụ: nợ thấp, dòng tiền đều, biên lợi nhuận cao, ROIC > 10%) đã được điều chỉnh cho phù hợp với dữ liệu của các công ty niêm yết tại Việt Nam.
    4.  **Agent Tổng hợp Khuyến nghị từ các Công ty Chứng khoán:** Tự động thu thập, tóm tắt và tổng hợp các báo cáo phân tích, khuyến nghị từ các công ty chứng khoán lớn (SSI, VNDS, HSC, VCSC...). Agent sẽ đưa ra một cái nhìn tổng quan về "sự đồng thuận của thị trường".
    5.  **Agent Phân tích Rủi ro T+2.5:** Một tính năng độc đáo, mô phỏng và cảnh báo về rủi ro biến động giá trong giai đoạn 2.5 ngày chờ cổ phiếu về tài khoản, một yếu tố rủi ro đặc thù của thị trường Việt Nam.

*   **Điểm khác biệt chính:**
    *   **Tập trung vào giảm thiểu rủi ro:** Không chỉ tìm kiếm lợi nhuận, mà ưu tiên hàng đầu là bảo vệ vốn cho nhà đầu tư.
    *   **Bối cảnh hóa cho Việt Nam:** Các phân tích và cảnh báo được thiết kế riêng cho các đặc thù của thị trường Việt Nam.
    *   **Tự động hóa sự khôn ngoan:** Mô phỏng quy trình làm việc của các chuyên gia phân tích, giúp người dùng không chuyên vẫn có thể đưa ra quyết định thông minh.

---

## Target Users

Sản phẩm **TradingAgents (Phiên bản Việt Nam)** được thiết kế và tối ưu hóa cho một nhóm đối tượng duy nhất: **Nhà đầu tư cá nhân tại Việt Nam (thường được gọi là F0, Fn)**.

*   **Hồ sơ (Profile):**
    *   Là những người có công việc chính không thuộc lĩnh vực tài chính.
    *   Có kiến thức về thị trường từ cơ bản đến trung bình, hoặc mới bắt đầu tìm hiểu.
    *   Ưu tiên hàng đầu là **bảo toàn vốn** và tìm kiếm lợi nhuận ổn định, không phải "lướt sóng" mạo hiểm.
    *   Không có nhiều thời gian để tự mình nghiên cứu sâu hàng ngày và cần một công cụ hiệu quả để tiết kiệm thời gian.

*   **Hành vi và Nhu cầu (Behaviors and Needs):**
    *   **Hành vi:** Thường cảm thấy bị ngợp bởi lượng thông tin khổng lồ và khó phân biệt tin tức đáng tin cậy. Dễ bị ảnh hưởng bởi tâm lý đám đông (FOMO, FUD). Gặp khó khăn khi phải đọc báo cáo tài chính phức tạp hoặc phân tích biểu đồ kỹ thuật.
    *   **Nhu cầu:** Cần một công cụ **đáng tin cậy** để "dịch" các thông tin tài chính phức tạp thành những khuyến nghị **đơn giản, rõ ràng và có tính hành động cao**. Họ cần được **cảnh báo sớm về các rủi ro** tiềm ẩn và muốn có một phương pháp đầu tư có hệ thống mà không tốn quá nhiều thời gian.

*   **Mục tiêu (Goals):**
    *   Tránh được các cạm bẫy và thua lỗ không đáng có.
    *   Xây dựng một danh mục đầu tư dài hạn, an toàn và hiệu quả.
    *   Tự tin hơn vào các quyết định đầu tư của mình, giảm bớt sự phụ thuộc vào cảm tính và tin đồn.

---

## Goals & Success Metrics

Để đảm bảo **TradingAgents (Phiên bản Việt Nam)** thực sự mang lại giá trị cho nhà đầu tư cá nhân, chúng tôi đặt ra các mục tiêu rõ ràng và có thể đo lường được.

#### **Mục tiêu Kinh doanh (Business Objectives)**

*   **Tăng cường sự tự tin của người dùng:**
    *   **Thước đo:** Tỷ lệ người dùng báo cáo cảm thấy tự tin hơn trong các quyết định đầu tư của họ (đo lường qua khảo sát người dùng hàng quý).
    *   **Mục tiêu:** Đạt 80% người dùng báo cáo tăng sự tự tin sau 3 tháng sử dụng sản phẩm.
*   **Giảm thiểu rủi ro cho người dùng:**
    *   **Thước đo:** Tỷ lệ các giao dịch được thực hiện dựa trên khuyến nghị của hệ thống có mức độ rủi ro thấp hơn so với mức trung bình của thị trường (đo lường qua phân tích dữ liệu giao dịch mô phỏng hoặc thực tế nếu có).
    *   **Mục tiêu:** Giảm 15% số lượng giao dịch có rủi ro cao trong danh mục của người dùng sử dụng hệ thống sau 6 tháng.
*   **Nâng cao chất lượng quyết định đầu tư:**
    *   **Thước đo:** Tỷ lệ người dùng tuân thủ các cảnh báo rủi ro của hệ thống (ví dụ: không mua cổ phiếu có cảnh báo thao túng, không thực hiện giao dịch có rủi ro T+2.5 cao).
    *   **Mục tiêu:** Đạt 70% người dùng tuân thủ các cảnh báo rủi ro quan trọng của hệ thống.

#### **Thước đo Thành công của Người dùng (User Success Metrics)**

*   **Tăng cường hiểu biết về thị trường:**
    *   **Thước đo:** Mức độ tương tác với các báo cáo phân tích chuyên sâu và các giải thích về rủi ro từ các agent (số lượt xem, thời gian đọc).
    *   **Mục tiêu:** Tăng 20% thời gian trung bình người dùng dành cho việc đọc các báo cáo phân tích và cảnh báo rủi ro.
*   **Cải thiện khả năng nhận diện rủi ro:**
    *   **Thước đo:** Tỷ lệ người dùng có thể tự nhận diện và giải thích các loại rủi ro khác nhau (ví dụ: rủi ro vĩ mô, rủi ro thao túng, rủi ro T+2.5) sau khi sử dụng sản phẩm (đo lường qua các bài kiểm tra kiến thức nhỏ hoặc khảo sát).
    *   **Mục tiêu:** 60% người dùng có thể giải thích được ít nhất 3 loại rủi ro đặc thù của thị trường Việt Nam.
*   **Đơn giản hóa quy trình phân tích:**
    *   **Thước đo:** Tỷ lệ người dùng báo cáo rằng sản phẩm giúp họ tiết kiệm thời gian và công sức trong việc phân tích thị trường (khảo sát).
    *   **Mục tiêu:** 90% người dùng cảm thấy sản phẩm giúp họ tiết kiệm thời gian phân tích.

#### **Chỉ số Hiệu suất Chính (Key Performance Indicators - KPIs)**

*   **Tỷ lệ giữ chân người dùng (Retention Rate):**
    *   **Định nghĩa & Mục tiêu:** Tỷ lệ người dùng tiếp tục sử dụng sản phẩm sau 1 tháng, 3 tháng, 6 tháng. Mục tiêu: >70% sau 3 tháng.
*   **Tần suất sử dụng (Frequency of Use):**
    *   **Định nghĩa & Mục tiêu:** Số lần trung bình người dùng truy cập và tương tác với hệ thống mỗi tuần. Mục tiêu: >3 lần/tuần.
*   **Số lượng cảnh báo rủi ro được tạo và tương tác:**
    *   **Định nghĩa & Mục tiêu:** Tổng số cảnh báo rủi ro (thao túng, T+2.5, vĩ mô) được hệ thống tạo ra và tỷ lệ người dùng xem/tương tác với các cảnh báo đó. Mục tiêu: Tỷ lệ tương tác >50%.
*   **Tỷ lệ chuyển đổi từ khuyến nghị sang hành động (nếu có tính năng giao dịch mô phỏng):**
    *   **Định nghĩa & Mục tiêu:** Tỷ lệ người dùng thực hiện giao dịch mô phỏng theo khuyến nghị của hệ thống. Mục tiêu: >40%.

---

## MVP Scope

Phiên bản MVP của **TradingAgents (Phiên bản Việt Nam)** sẽ tập trung vào việc cung cấp các công cụ phân tích và cảnh báo rủi ro thiết yếu nhất cho **một cổ phiếu cụ thể tại một thời điểm**, giúp nhà đầu tư cá nhân đưa ra quyết định an toàn hơn. Người dùng sẽ nhập mã cổ phiếu và nhận được tất cả các thông tin trong phạm vi MVP cho cổ phiếu đó.

#### **Các tính năng cốt lõi (Core Features - Must Have)**

1.  **Phân tích và Cảnh báo Rủi ro Vĩ mô Việt Nam:**
    *   **Mô tả:** Hệ thống sẽ tự động thu thập và phân tích các chỉ số kinh tế vĩ mô quan trọng của Việt Nam (GDP, lạm phát, lãi suất, chính sách tiền tệ) từ các nguồn dữ liệu công khai.
    *   **Giá trị:** Cung cấp cái nhìn tổng quan về bối cảnh kinh tế, giúp người dùng nhận diện rủi ro hệ thống và điều chỉnh chiến lược đầu tư phù hợp.
    *   **Đầu ra:** Báo cáo tóm tắt về tình hình vĩ mô và cảnh báo về các yếu tố rủi ro/cơ hội lớn, có liên hệ đến cổ phiếu đang phân tích.

2.  **Sàng lọc Cổ phiếu "An toàn" theo Chuẩn Mực:**
    *   **Mô tả:** Khi người dùng nhập mã cổ phiếu, hệ thống sẽ phân tích dữ liệu tài chính cơ bản của **cổ phiếu đó** và áp dụng các tiêu chí sàng lọc nghiêm ngặt (ví dụ: nợ thấp, dòng tiền ổn định, ROE/ROIC cao) để đánh giá "điểm an toàn tài chính" của doanh nghiệp.
    *   **Giá trị:** Giúp nhà đầu tư nhanh chóng xác định liệu **cổ phiếu đang xem xét** có nền tảng vững chắc, giảm thiểu rủi ro lựa chọn sai lầm.
    *   **Đầu ra:** Điểm số an toàn, các chỉ số tài chính chính, và lý do tại sao cổ phiếu được/không được coi là "an toàn".

3.  **Cảnh báo Rủi ro Thao túng giá (Dấu hiệu "Đội Lái"):**
    *   **Mô tả:** Hệ thống sẽ phân tích các mẫu hình giao dịch bất thường (khối lượng đột biến, biến động giá không rõ nguyên nhân, các lệnh lớn bất thường) của **cổ phiếu được chọn** để phát hiện các dấu hiệu có thể liên quan đến hoạt động thao túng giá.
    *   **Giá trị:** Bảo vệ nhà đầu tư khỏi các cổ phiếu có nguy cơ bị làm giá, một rủi ro phổ biến trên thị trường Việt Nam.
    *   **Đầu ra:** Cảnh báo "Rủi ro thao túng cao/trung bình/thấp" và các dấu hiệu cụ thể được phát hiện cho cổ phiếu đó.

4.  **Phân tích và Cảnh báo Rủi ro T+2.5:**
    *   **Mô tả:** Khi người dùng xem xét **một cổ phiếu cụ thể**, hệ thống sẽ mô phỏng và tính toán rủi ro biến động giá trong khoảng thời gian chờ cổ phiếu về tài khoản (T+2.5).
    *   **Giá trị:** Giúp nhà đầu tư nhận thức rõ và quản lý rủi ro trong giai đoạn thanh toán, tránh các bất ngờ không mong muốn.
    *   **Đầu ra:** Cảnh báo "Rủi ro T+2.5 cao/trung bình/thấp" và dự báo biến động giá tiềm năng cho cổ phiếu đó.

5.  **Giao diện người dùng đơn giản và trực quan:**
    *   **Mô tả:** Giao diện được thiết kế tối giản, dễ sử dụng, tập trung vào việc nhập mã cổ phiếu và hiển thị tất cả các cảnh báo rủi ro, phân tích liên quan đến **cổ phiếu đó** một cách rõ ràng, không gây nhiễu.
    *   **Giá trị:** Đảm bảo nhà đầu tư cá nhân có thể dễ dàng tiếp cận và sử dụng sản phẩm mà không cần kiến thức công nghệ phức tạp.

#### **Các tính năng ngoài phạm vi MVP (Out of Scope for MVP)**

*   **Phân tích kỹ thuật chuyên sâu:** Các chỉ báo kỹ thuật phức tạp, biểu đồ nến, xu hướng giá sẽ không được ưu tiên trong MVP.
*   **Phân tích tin tức và tâm lý thị trường:** Việc thu thập và phân tích tin tức, tâm lý từ mạng xã hội sẽ được phát triển ở các giai đoạn sau.
*   **Tích hợp giao dịch trực tiếp:** Không có khả năng đặt lệnh mua/bán trực tiếp qua hệ thống.
*   **Các tính năng xã hội/cộng đồng:** Không có diễn đàn, chat, hoặc tính năng chia sẻ.
*   **Quản lý danh mục đầu tư:** Không có tính năng theo dõi danh mục, lợi nhuận/thua lỗ tổng thể.
*   **Tùy chỉnh nâng cao:** Các tùy chỉnh sâu về chiến lược phân tích hoặc ngưỡng cảnh báo sẽ không có trong MVP.

#### **Tiêu chí thành công của MVP (MVP Success Criteria)**

*   Người dùng có thể dễ dàng nhập mã cổ phiếu và nhận được báo cáo phân tích, cảnh báo rủi ro đầy đủ cho cổ phiếu đó.
*   Người dùng có thể dễ dàng nhận diện và hiểu các cảnh báo rủi ro từ hệ thống cho cổ phiếu được chọn.
*   Người dùng báo cáo cảm thấy an toàn và tự tin hơn khi đưa ra quyết định đầu tư dựa trên thông tin từ sản phẩm cho cổ phiếu cụ thể.
*   Sản phẩm hoạt động ổn định, cung cấp dữ liệu chính xác và kịp thời cho từng cổ phiếu.

---

## Post-MVP Vision

Sau khi phiên bản MVP của **TradingAgents (Phiên bản Việt Nam)** được triển khai thành công và chứng minh được giá trị trong việc giúp nhà đầu tư cá nhân giảm thiểu rủi ro, tầm nhìn của chúng tôi là mở rộng sản phẩm để cung cấp một bộ công cụ phân tích toàn diện hơn, đồng thời nâng cao trải nghiệm người dùng và khả năng cá nhân hóa.

#### **Giai đoạn 2: Mở rộng Phân tích và Cá nhân hóa**

1.  **Phân tích Kỹ thuật Chuyên sâu:**
    *   **Mô tả:** Tích hợp các agent phân tích kỹ thuật (Technical Analyst) để cung cấp các chỉ báo kỹ thuật nâng cao (ví dụ: MACD, RSI, Bollinger Bands, Fibonacci), mẫu hình nến, và dự báo xu hướng giá.
    *   **Giá trị:** Giúp người dùng có thêm góc nhìn về thời điểm giao dịch và xác định các điểm vào/ra tiềm năng.

2.  **Phân tích Tin tức và Tâm lý Thị trường:**
    *   **Mô tả:** Triển khai các agent News Analyst và Sentiment Analyst để thu thập, phân tích và tóm tắt tin tức từ nhiều nguồn (báo chí, diễn đàn, mạng xã hội) và đánh giá tâm lý thị trường.
    *   **Giá trị:** Cung cấp thông tin kịp thời về các sự kiện có thể ảnh hưởng đến giá cổ phiếu và giúp người dùng tránh các quyết định dựa trên cảm xúc.

3.  **Tổng hợp Khuyến nghị từ các Công ty Chứng khoán:**
    *   **Mô tả:** Agent mới sẽ tự động thu thập, tóm tắt và tổng hợp các báo cáo phân tích, khuyến nghị từ các công ty chứng khoán lớn tại Việt Nam (SSI, VNDS, HSC, VCSC...).
    *   **Giá trị:** Cung cấp một cái nhìn tổng quan về "sự đồng thuận của thị trường" từ các chuyên gia, giúp người dùng có thêm thông tin tham khảo đa chiều.

4.  **Cá nhân hóa Cảnh báo và Báo cáo:**
    *   **Mô tả:** Cho phép người dùng tùy chỉnh ngưỡng cảnh báo rủi ro (ví dụ: mức độ rủi ro thao túng chấp nhận được, độ nhạy của cảnh báo T+2.5).
    *   **Giá trị:** Nâng cao trải nghiệm người dùng bằng cách điều chỉnh sản phẩm phù hợp với khẩu vị rủi ro và phong cách đầu tư cá nhân.

#### **Tầm nhìn dài hạn:**

*   **Mở rộng sang các loại tài sản khác:** Ngoài cổ phiếu, có thể mở rộng sang phân tích các loại tài sản khác như phái sinh, trái phiếu, hoặc tiền điện tử (nếu phù hợp với thị trường Việt Nam).
*   **Học máy cá nhân hóa:** Hệ thống học hỏi từ hành vi và quyết định của người dùng để đưa ra các khuyến nghị ngày càng phù hợp hơn.
*   **Tích hợp AI tạo sinh:** Sử dụng AI tạo sinh để tạo ra các báo cáo phân tích chuyên sâu, dễ hiểu hơn, hoặc thậm chí là các kịch bản thị trường tiềm năng.

---