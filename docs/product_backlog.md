# Product Backlog: TradingAgents (<PERSON><PERSON><PERSON> bản Việt Nam)

## Epics (MVP)

### 1. Epic: Phân tích và Cảnh báo Rủi ro Vĩ mô Việt Nam
*   **Mục tiêu:** Cung cấp cho người dùng cái nhìn tổng quan về bối cảnh kinh tế vĩ mô Việt Nam và các rủi ro/cơ hội liên quan đến cổ phiếu đang phân tích.
*   **Tính năng cốt lõi:** <PERSON>hu thập, phân tích chỉ số vĩ mô, hiển thị báo cáo tóm tắt và cảnh báo.

#### **User Stories cho Epic: Phân tích và Cảnh báo Rủi ro Vĩ mô Việt Nam**

#### **US1.1: Là một nhà đầu tư, tôi muốn xem báo cáo tóm tắt về tình hình kinh tế vĩ mô Việt Nam để hiểu bối cảnh chung của thị trường.**

*   **M<PERSON> tả:** <PERSON><PERSON> thống sẽ hiển thị một báo cáo ngắn gọn, dễ hiểu về các chỉ số kinh tế vĩ mô chính của Việt Nam (ví dụ: GDP, lạm phát, lãi suất, chính sách tiền tệ).
*   **Acceptance Criteria:**
    *   Khi người dùng truy cập tính năng phân tích vĩ mô, hệ thống phải hiển thị báo cáo tóm tắt.
    *   Báo cáo phải bao gồm các chỉ số vĩ mô chính và xu hướng của chúng (tăng/giảm/ổn định).
    *   Ngôn ngữ báo cáo phải đơn giản, dễ hiểu đối với nhà đầu tư cá nhân.
    *   Dữ liệu vĩ mô phải được cập nhật định kỳ (ví dụ: hàng tháng/quý).

#### **US1.2: Là một nhà đầu tư, tôi muốn biết tác động tiềm năng của các yếu tố vĩ mô lên cổ phiếu tôi đang quan tâm để đánh giá rủi ro hệ thống.**

*   **Mô tả:** Khi người dùng nhập mã cổ phiếu, hệ thống sẽ phân tích và chỉ ra mối liên hệ giữa các yếu tố vĩ mô và ngành/doanh nghiệp của cổ phiếu đó, kèm theo cảnh báo về rủi ro/cơ hội.
*   **Acceptance Criteria:**
    *   Khi người dùng nhập mã cổ phiếu, hệ thống phải hiển thị phân tích tác động vĩ mô liên quan đến cổ phiếu đó.
    *   Phân tích phải giải thích rõ ràng yếu tố vĩ mô nào (ví dụ: lãi suất tăng) có thể ảnh hưởng đến cổ phiếu (ví dụ: ngành ngân hàng hưởng lợi).
    *   Hệ thống phải đưa ra cảnh báo (ví dụ: "Rủi ro vĩ mô cao/trung bình/thấp") dựa trên mức độ nhạy cảm của cổ phiếu với các yếu tố vĩ mô.
    *   Thông tin phải được trình bày trực quan (ví dụ: biểu đồ đơn giản, mũi tên chỉ xu hướng).

### 2. Epic: Sàng lọc Cổ phiếu "An toàn" theo Chuẩn Mực
*   **Mục tiêu:** Giúp người dùng nhanh chóng đánh giá mức độ an toàn tài chính của một cổ phiếu dựa trên các tiêu chí đã định.
*   **Tính năng cốt lõi:** Phân tích dữ liệu tài chính, áp dụng bộ lọc an toàn, hiển thị điểm an toàn và lý do.

#### **User Stories cho Epic: Sàng lọc Cổ phiếu 'An toàn' theo Chuẩn Mực**

#### **US2.1: Là một nhà đầu tư, tôi muốn nhập mã cổ phiếu và nhận được "Điểm An toàn Tài chính" của doanh nghiệp đó để nhanh chóng đánh giá sức khỏe tài chính. (Hoàn thành)**

*   **Mô tả:** Người dùng nhập mã cổ phiếu (ví dụ: HPG). Hệ thống sẽ tự động phân tích các chỉ số tài chính cơ bản (ví dụ: P/E, P/B, ROE, ROA, nợ/vốn chủ sở hữu, dòng tiền hoạt động) và đưa ra một "Điểm An toàn Tài chính" (ví dụ: từ 1 đến 10 hoặc Thấp/Trung bình/Cao).
*   **Acceptance Criteria:**
    *   Khi người dùng nhập mã cổ phiếu hợp lệ, hệ thống phải hiển thị "Điểm An toàn Tài chính" trong vòng X giây.
    *   Điểm an toàn phải được tính toán dựa trên một tập hợp các chỉ số tài chính đã được định nghĩa trước.
    *   Hệ thống phải giải thích ngắn gọn ý nghĩa của điểm số (ví dụ: "Điểm 8/10 cho thấy doanh nghiệp có nền tảng tài chính vững chắc").
    *   Dữ liệu tài chính được sử dụng phải là dữ liệu công khai, cập nhật nhất.

#### **US2.2: Là một nhà đầu tư, tôi muốn xem các tiêu chí cụ thể mà hệ thống dùng để đánh giá "Điểm An toàn Tài chính" để hiểu rõ hơn về phân tích.**

*   **Mô tả:** Bên cạnh "Điểm An toàn Tài chính", hệ thống sẽ liệt kê các tiêu chí chính đã được sử dụng (ví dụ: "Nợ/Vốn chủ sở hữu < 0.5", "ROE > 15%", "Dòng tiền hoạt động dương liên tục 3 năm") và cho biết cổ phiếu đó có đạt từng tiêu chí hay không.
*   **Acceptance Criteria:**
    *   Khi xem "Điểm An toàn Tài chính", người dùng có thể truy cập danh sách các tiêu chí đánh giá.
    *   Mỗi tiêu chí phải được hiển thị rõ ràng cùng với trạng thái "Đạt" hoặc "Không đạt" cho cổ phiếu đang xem.
    *   Hệ thống phải cung cấp giải thích ngắn gọn về từng tiêu chí nếu người dùng yêu cầu (ví dụ: hover chuột hoặc click).

### 3. Epic: Cảnh báo Rủi ro Thao túng giá (Dấu hiệu "Đội Lái")
*   **Mục tiêu:** Bảo vệ người dùng khỏi các cổ phiếu có nguy cơ bị làm giá bằng cách phát hiện các dấu hiệu giao dịch bất thường.
*   **Tính năng cốt lõi:** Phân tích mẫu hình giao dịch, cảnh báo rủi ro thao túng và hiển thị dấu hiệu cụ thể.

#### **User Stories cho Epic: Cảnh báo Rủi ro Thao túng giá (Dấu hiệu 'Đội Lái')**

#### **US3.1: Là một nhà đầu tư, tôi muốn nhận được cảnh báo về nguy cơ thao túng giá của một cổ phiếu để tránh các cạm bẫy.**

*   **Mô tả:** Khi người dùng nhập mã cổ phiếu, hệ thống sẽ phân tích các dấu hiệu bất thường (khối lượng đột biến, biến động giá không rõ nguyên nhân, các lệnh lớn bất thường) và đưa ra mức độ rủi ro thao túng.
*   **Acceptance Criteria:**
    *   Khi người dùng nhập mã cổ phiếu hợp lệ, hệ thống phải hiển thị mức độ rủi ro thao túng (ví dụ: Cao/Trung bình/Thấp).
    *   Mức độ rủi ro phải được tính toán dựa trên các thuật toán phát hiện bất thường trong dữ liệu giao dịch.
    *   Cảnh báo phải được hiển thị rõ ràng và dễ hiểu.

#### **US3.2: Là một nhà đầu tư, tôi muốn xem các dấu hiệu cụ thể mà hệ thống dùng để cảnh báo thao túng giá để tôi có thể tự đánh giá và học hỏi.**

*   **Mô tả:** Hệ thống sẽ liệt kê các dấu hiệu cụ thể đã được phát hiện (ví dụ: "Khối lượng giao dịch tăng đột biến 500% trong 3 ngày mà không có tin tức hỗ trợ", "Giá tăng trần liên tục với thanh khoản thấp", "Xuất hiện các lệnh mua/bán lớn bất thường vào cuối phiên").
*   **Acceptance Criteria:**
    *   Khi xem cảnh báo thao túng, người dùng có thể truy cập danh sách các dấu hiệu cụ thể.
    *   Mỗi dấu hiệu phải được mô tả rõ ràng và có thể liên kết với dữ liệu giao dịch (nếu có thể, ví dụ: hiển thị trên biểu đồ).
    *   Hệ thống phải giải thích ngắn gọn ý nghĩa của từng dấu hiệu.

### 4. Epic: Phân tích và Cảnh báo Rủi ro T+2.5
*   **Mục tiêu:** Giúp người dùng nhận thức và quản lý rủi ro biến động giá trong giai đoạn chờ cổ phiếu về tài khoản.
*   **Tính năng cốt lõi:** Mô phỏng, tính toán rủi ro T+2.5, hiển thị cảnh báo và dự báo biến động.

#### **User Stories cho Epic: Phân tích và Cảnh báo Rủi ro T+2.5**

#### **US4.1: Là một nhà đầu tư, tôi muốn biết mức độ rủi ro biến động giá của cổ phiếu trong giai đoạn T+2.5 để đưa ra quyết định mua/bán phù hợp.**

*   **Mô tả:** Khi người dùng xem xét một cổ phiếu, hệ thống sẽ mô phỏng và tính toán mức độ rủi ro biến động giá (ví dụ: Cao/Trung bình/Thấp) trong khoảng thời gian chờ cổ phiếu về tài khoản (T+2.5).
*   **Acceptance Criteria:**
    *   Khi người dùng nhập mã cổ phiếu hợp lệ, hệ thống phải hiển thị mức độ rủi ro T+2.5.
    *   Mức độ rủi ro phải được tính toán dựa trên các yếu tố như biến động lịch sử, thanh khoản, và các sự kiện sắp tới (nếu có thể).
    *   Cảnh báo phải được hiển thị rõ ràng và dễ hiểu.

#### **US4.2: Là một nhà đầu tư, tôi muốn xem dự báo biến động giá tiềm năng trong giai đoạn T+2.5 để có cái nhìn cụ thể hơn về rủi ro.**

*   **Mô tả:** Hệ thống sẽ cung cấp một dự báo đơn giản về biên độ biến động giá tiềm năng (ví dụ: "có thể biến động trong khoảng +/- X% trong 2.5 ngày tới") hoặc các kịch bản giá có thể xảy ra trong giai đoạn T+2.5.
*   **Acceptance Criteria:**
    *   Khi xem cảnh báo rủi ro T+2.5, người dùng có thể xem dự báo biến động giá tiềm năng.
    *   Dự báo phải bao gồm biên độ giá hoặc các kịch bản giá có thể xảy ra.
    *   Hệ thống phải giải thích các yếu tố chính ảnh hưởng đến dự báo này.

### 5. Epic: Giao diện Người dùng Đơn giản và Trực quan
*   **Mục tiêu:** Đảm bảo người dùng có thể dễ dàng nhập mã cổ phiếu và tiếp cận các thông tin phân tích, cảnh báo một cách rõ ràng.
*   **Tính năng cốt lõi:** Thiết kế giao diện tối giản, tập trung vào hiển thị kết quả phân tích cho từng cổ phiếu.

#### **User Stories cho Epic: Giao diện Người dùng Đơn giản và Trực quan**

#### **US5.1: Là một nhà đầu tư, tôi muốn có một ô tìm kiếm mã cổ phiếu rõ ràng và dễ sử dụng để nhanh chóng bắt đầu phân tích. (Hoàn thành)**

*   **Mô tả:** Hệ thống sẽ cung cấp một ô nhập liệu (input field) nổi bật trên giao diện chính, cho phép người dùng gõ mã cổ phiếu.
*   **Acceptance Criteria:**
    *   Ô tìm kiếm phải hiển thị rõ ràng trên màn hình chính khi ứng dụng khởi động.
    *   Người dùng có thể nhập mã cổ phiếu (ví dụ: "HPG", "VCB") vào ô này.
    *   Hệ thống phải có tính năng gợi ý mã cổ phiếu khi người dùng đang gõ (autocomplete) để tránh lỗi nhập liệu.
    *   Khi người dùng nhấn Enter hoặc nút tìm kiếm, hệ thống phải chuyển đến màn hình hiển thị kết quả phân tích của cổ phiếu đó.

#### **US5.2: Là một nhà đầu tư, tôi muốn các thông tin phân tích và cảnh báo rủi ro được hiển thị rõ ràng, dễ đọc và dễ hiểu trên một màn hình duy nhất. (Hoàn thành)**

*   **Mô tả:** Sau khi nhập mã cổ phiếu, hệ thống sẽ hiển thị tất cả các kết quả phân tích (vĩ mô liên quan, điểm an toàn tài chính, cảnh báo thao túng, rủi ro T+2.5) trên một màn hình tổng quan, được tổ chức một cách khoa học.
*   **Acceptance Criteria:**
    *   Màn hình kết quả phân tích phải hiển thị tên đầy đủ của công ty và mã cổ phiếu.
    *   Các phần thông tin (Vĩ mô, An toàn Tài chính, Thao túng, T+2.5) phải được phân tách rõ ràng bằng tiêu đề hoặc các khối nội dung.
    *   Các cảnh báo rủi ro phải được làm nổi bật (ví dụ: màu sắc, biểu tượng) để người dùng dễ dàng nhận thấy.
    *   Thông tin phải được trình bày bằng ngôn ngữ đơn giản, tránh thuật ngữ chuyên ngành phức tạp.
    *   Người dùng không cần phải điều hướng qua nhiều màn hình để xem các thông tin chính.

#### **US5.3: Là một nhà đầu tư, tôi muốn có khả năng xem lại các cổ phiếu đã phân tích gần đây để tiết kiệm thời gian nhập lại.**

*   **Mô tả:** Hệ thống sẽ lưu lại lịch sử các mã cổ phiếu mà người dùng đã tìm kiếm và hiển thị chúng ở một vị trí dễ tiếp cận.
*   **Acceptance Criteria:**
    *   Hệ thống phải lưu trữ ít nhất 5 mã cổ phiếu gần nhất mà người dùng đã tìm kiếm.
    *   Lịch sử tìm kiếm phải hiển thị ở một vị trí thuận tiện (ví dụ: dưới ô tìm kiếm hoặc trong một menu riêng).
    *   Người dùng có thể click vào một mã cổ phiếu trong lịch sử để xem lại kết quả phân tích của cổ phiếu đó.