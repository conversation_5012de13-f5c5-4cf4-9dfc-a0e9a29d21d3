import json
from langchain_core.messages import HumanMessage
from typing import Annotated
from langchain_core.messages import RemoveMessage
from langchain_core.tools import tool
import tradingagents.dataflows.interface as interface
from tradingagents.default_config import DEFAULT_CONFIG
from langchain_core.messages import HumanMessage


def create_msg_delete():
    def delete_messages(state):
        """Clear messages and add placeholder for Anthropic compatibility"""
        messages = state["messages"]
        
        # Remove all messages
        removal_operations = [RemoveMessage(id=m.id) for m in messages]
        
        # Add a minimal placeholder message
        placeholder = HumanMessage(content="Continue")
        
        return {"messages": removal_operations + [placeholder]}
    
    return delete_messages


class Toolkit:
    _config = DEFAULT_CONFIG.copy()

    @classmethod
    def update_config(cls, config):
        """Update the class-level configuration."""
        cls._config.update(config)

    @property
    def config(self):
        """Access the configuration."""
        return self._config

    def __init__(self, config=None):
        if config:
            self.update_config(config)

    @staticmethod
    @tool
    def get_YFin_data_online(
        symbol: Annotated[str, "ticker symbol of the company"],
        start_date: Annotated[str, "Start date in yyyy-mm-dd format"],
        end_date: Annotated[str, "End date in yyyy-mm-dd format"],
    ) -> str:
        """
        Retrieve the stock price data for a given ticker symbol from Yahoo Finance.
        Args:
            symbol (str): Ticker symbol of the company, e.g. ACB, SHB
            start_date (str): Start date in yyyy-mm-dd format
            end_date (str): End date in yyyy-mm-dd format
        Returns:
            str: A formatted dataframe containing the stock price data for the specified ticker symbol in the specified date range.
        """

        result_data = interface.get_YFin_data_online(symbol, start_date, end_date)

        return result_data

    
    @staticmethod
    @tool
    def get_stockstats_indicators_report_online(
        symbol: Annotated[str, "ticker symbol of the company"],
        indicator: Annotated[
            str, "technical indicator to get the analysis and report of"
        ],
        curr_date: Annotated[
            str, "The current trading date you are trading on, YYYY-mm-dd"
        ],
        look_back_days: Annotated[int, "how many days to look back"] = 30,
    ) -> str:
        """
        Retrieve stock stats indicators for a given ticker symbol and indicator.
        Args:
            symbol (str): Ticker symbol of the company, e.g. ACB, SHB
            indicator (str): Technical indicator to get the analysis and report of
            curr_date (str): The current trading date you are trading on, YYYY-mm-dd
            look_back_days (int): How many days to look back, default is 30
        Returns:
            str: A formatted dataframe containing the stock stats indicators for the specified ticker symbol and indicator.
        """

        result_stockstats = interface.get_stock_stats_indicators_window(
            symbol, indicator, curr_date, look_back_days, True
        )

        return result_stockstats

    @staticmethod
    @tool
    def get_google_news(
        query: Annotated[str, "Query to search with"],
        curr_date: Annotated[str, "Curr date in yyyy-mm-dd format"],
    ):
        """
        Retrieve the latest news from Google News based on a query and date range.
        Args:
            query (str): Query to search with
            curr_date (str): Current date in yyyy-mm-dd format
            look_back_days (int): How many days to look back
        Returns:
            str: A formatted string containing the latest news from Google News based on the query and date range.
        """

        google_news_results = interface.get_google_news(query, curr_date, 7)
        return google_news_results

    @staticmethod
    @tool
    def get_sentiment_news(
        ticker: Annotated[str, "the company's ticker"],
        curr_date: Annotated[str, "Current date in yyyy-mm-dd format"],
    ):
        """
        Retrieve the latest news about a given stock by using LLM's web search capabilities.
        Args:
            ticker (str): Ticker of a company. e.g. ACB, SHB
            curr_date (str): Current date in yyyy-mm-dd format
        Returns:
            str: A formatted string containing the latest news about the company on the given date.
        """

        results = interface.get_sentiment_news(ticker, curr_date)
        return results

    @staticmethod
    @tool
    def get_global_news(
        curr_date: Annotated[str, "Current date in yyyy-mm-dd format"],
    ):
        """
        Retrieve the latest macroeconomics news on a given date using LLM's web search capabilities.
        Args:
            curr_date (str): Current date in yyyy-mm-dd format
        Returns:
            str: A formatted string containing the latest macroeconomic news on the given date.
        """

        results = interface.get_global_news(curr_date)

        return results

    @staticmethod
    @tool
    def get_fundamentals(
        ticker: Annotated[str, "the company's ticker"],
        curr_date: Annotated[str, "Current date in yyyy-mm-dd format"],
    ):
        """
        Retrieve the latest fundamental information about a given stock on a given date by using LLM's web search capabilities.
        Args:
            ticker (str): Ticker of a company. e.g. ACB, SHB
            curr_date (str): Current date in yyyy-mm-dd format
        Returns:
            str: A formatted string containing the latest fundamental information about the company on the given date.
        """

        results = interface.get_fundamentals(
            ticker, curr_date
        )

        return results

    @staticmethod
    @tool
    def calculate_safety_score(
        ticker: Annotated[str, "the company's ticker"],
        fundamentals_data: Annotated[str, "Fundamental data of the company"],
    ) -> str:
        """
        Tính toán "Điểm An toàn Tài chính" cho một cổ phiếu dựa trên dữ liệu cơ bản.
        Điểm số từ 1 (rất rủi ro) đến 10 (rất an toàn).
        """
        # This tool will be invoked by the SafetyAnalyst agent, which contains the LLM logic
        # For now, it's a placeholder that simply returns the fundamentals data
        return f"Đã nhận dữ liệu cơ bản cho {ticker} để tính điểm an toàn: {fundamentals_data}"

    # Advanced Tools for Custom Agents

    @staticmethod
    @tool
    def get_financial_statements(
        ticker: Annotated[str, "Stock ticker symbol"],
        statement_type: Annotated[str, "Type of statement: 'income', 'balance', 'cashflow', 'all'"] = "all",
        periods: Annotated[int, "Number of periods to retrieve"] = 4
    ) -> str:
        """
        Get detailed financial statements from the financial API.

        Args:
            ticker: Stock ticker symbol
            statement_type: Type of statement ('income', 'balance', 'cashflow', 'all')
            periods: Number of periods to retrieve (default: 4)

        Returns:
            Formatted financial statements data
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()

            if statement_type == "all":
                # Get all three statements
                income_data = financial_api.get_financial_statements(ticker, "income", "quarterly", periods)
                balance_data = financial_api.get_financial_statements(ticker, "balance", "quarterly", periods)
                cashflow_data = financial_api.get_financial_statements(ticker, "cashflow", "quarterly", periods)

                result = "# COMPREHENSIVE FINANCIAL STATEMENTS\n\n"
                result += financial_api.format_financial_data(income_data, "statements")
                result += "\n" + financial_api.format_financial_data(balance_data, "statements")
                result += "\n" + financial_api.format_financial_data(cashflow_data, "statements")

                return result
            else:
                data = financial_api.get_financial_statements(ticker, statement_type, "quarterly", periods)
                return financial_api.format_financial_data(data, "statements")

        except Exception as e:
            return f"Error retrieving financial statements: {e}"

    @staticmethod
    @tool
    def get_financial_ratios(
        ticker: Annotated[str, "Stock ticker symbol"],
        date: Annotated[str, "Specific date for analysis (YYYY-MM-DD format)"] = None
    ) -> str:
        """
        Get comprehensive financial ratios analysis.

        Args:
            ticker: Stock ticker symbol
            date: Specific date for analysis (YYYY-MM-DD format)

        Returns:
            Formatted financial ratios data
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()

            ratios_data = financial_api.get_financial_ratios(ticker, date)
            return financial_api.format_financial_data(ratios_data, "ratios")

        except Exception as e:
            return f"Error retrieving financial ratios: {e}"

    @staticmethod
    @tool
    def get_earnings_analysis(
        ticker: Annotated[str, "Stock ticker symbol"],
        quarters: Annotated[int, "Number of quarters to analyze"] = 8
    ) -> str:
        """
        Get detailed earnings analysis and trends.

        Args:
            ticker: Stock ticker symbol
            quarters: Number of quarters to analyze (default: 8)

        Returns:
            Formatted earnings analysis data
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()

            earnings_data = financial_api.get_earnings_data(ticker, quarters)
            return financial_api.format_financial_data(earnings_data, "earnings")

        except Exception as e:
            return f"Error retrieving earnings analysis: {e}"

    @staticmethod
    @tool
    def get_volume_analysis(
        ticker: Annotated[str, "Stock ticker symbol"],
        start_date: Annotated[str, "Start date for analysis (YYYY-MM-DD)"],
        end_date: Annotated[str, "End date for analysis (YYYY-MM-DD)"],
        analysis_type: Annotated[str, "Type of analysis: 'comprehensive', 'patterns', 'indicators'"] = "comprehensive"
    ) -> str:
        """
        Get comprehensive volume analysis data.

        Args:
            ticker: Stock ticker symbol
            start_date: Start date for analysis (YYYY-MM-DD)
            end_date: End date for analysis (YYYY-MM-DD)
            analysis_type: Type of analysis ('comprehensive', 'patterns', 'indicators')

        Returns:
            Formatted volume analysis data
        """
        try:
            from tradingagents.dataflows.trading_data_api import get_trading_api
            trading_api = get_trading_api()

            # Get volume data
            volume_data = trading_api.get_volume_analysis(ticker, start_date, end_date)

            if analysis_type == "comprehensive":
                # Get additional market data for comprehensive analysis
                price_data = trading_api.get_historical_prices(ticker, start_date, end_date)

                result = "# COMPREHENSIVE VOLUME ANALYSIS\n\n"
                result += trading_api.format_volume_analysis(volume_data)
                result += "\n## Price-Volume Correlation\n"
                result += trading_api.format_price_data(price_data)

                return result
            else:
                return trading_api.format_volume_analysis(volume_data)

        except Exception as e:
            return f"Error retrieving volume analysis: {e}"

    @staticmethod
    @tool
    def get_order_book_analysis(
        ticker: Annotated[str, "Stock ticker symbol"],
        depth: Annotated[int, "Order book depth levels to analyze"] = 20
    ) -> str:
        """
        Get detailed order book analysis.

        Args:
            ticker: Stock ticker symbol
            depth: Order book depth levels to analyze

        Returns:
            Formatted order book analysis
        """
        try:
            from tradingagents.dataflows.trading_data_api import get_trading_api
            trading_api = get_trading_api()

            order_book_data = trading_api.get_order_book(ticker, depth)

            # Format order book analysis
            result = "# ORDER BOOK ANALYSIS\n\n"

            if order_book_data:
                bids = order_book_data.get("bids", [])
                asks = order_book_data.get("asks", [])

                result += "## Order Book Depth\n"
                result += "| Level | Bid Price | Bid Size | Ask Price | Ask Size | Spread |\n"
                result += "|-------|-----------|----------|-----------|----------|--------|\n"

                for i in range(min(len(bids), len(asks), 10)):
                    bid_price = bids[i].get("price", 0) if i < len(bids) else 0
                    bid_size = bids[i].get("size", 0) if i < len(bids) else 0
                    ask_price = asks[i].get("price", 0) if i < len(asks) else 0
                    ask_size = asks[i].get("size", 0) if i < len(asks) else 0
                    spread = ask_price - bid_price if bid_price and ask_price else 0

                    result += f"| {i+1} | {bid_price:.2f} | {bid_size:,.0f} | {ask_price:.2f} | {ask_size:,.0f} | {spread:.2f} |\n"

                # Calculate order book metrics
                total_bid_volume = sum(bid.get("size", 0) for bid in bids[:10])
                total_ask_volume = sum(ask.get("size", 0) for ask in asks[:10])
                imbalance = (total_bid_volume - total_ask_volume) / (total_bid_volume + total_ask_volume) if (total_bid_volume + total_ask_volume) > 0 else 0

                result += f"\n## Order Book Metrics\n"
                result += f"- Total Bid Volume (Top 10): {total_bid_volume:,.0f}\n"
                result += f"- Total Ask Volume (Top 10): {total_ask_volume:,.0f}\n"
                result += f"- Order Book Imbalance: {imbalance:.2%}\n"
                result += f"- Bid-Ask Spread: {spread:.2f}\n"

            return result

        except Exception as e:
            return f"Error retrieving order book analysis: {e}"

    @staticmethod
    @tool
    def get_trading_patterns(
        ticker: Annotated[str, "Stock ticker symbol"],
        start_date: Annotated[str, "Start date for pattern analysis"],
        end_date: Annotated[str, "End date for pattern analysis"]
    ) -> str:
        """
        Identify and analyze trading patterns.

        Args:
            ticker: Stock ticker symbol
            start_date: Start date for pattern analysis
            end_date: End date for pattern analysis

        Returns:
            Trading patterns analysis
        """
        try:
            from tradingagents.dataflows.trading_data_api import get_trading_api
            trading_api = get_trading_api()

            # Get comprehensive market data for pattern analysis
            market_data = trading_api.get_comprehensive_market_data(ticker, start_date, end_date)

            result = "# TRADING PATTERNS ANALYSIS\n\n"
            result += market_data

            # Add pattern recognition analysis
            result += "\n## Pattern Recognition\n"
            result += "- Volume Breakout Patterns: Analyzing volume spikes above 2x average\n"
            result += "- Accumulation Patterns: Looking for sustained volume with price stability\n"
            result += "- Distribution Patterns: Identifying high volume with price weakness\n"
            result += "- Institutional Footprints: Large block trades and unusual volume\n"

            return result

        except Exception as e:
            return f"Error retrieving trading patterns: {e}"

    # Earnings Specialist Tools

    @staticmethod
    @tool
    def get_earnings_quality_analysis(
        ticker: Annotated[str, "Stock ticker symbol"],
        quarters: Annotated[int, "Number of quarters to analyze for trends"] = 8
    ) -> str:
        """
        Analyze earnings quality and sustainability.

        Args:
            ticker: Stock ticker symbol
            quarters: Number of quarters to analyze for trends

        Returns:
            Comprehensive earnings quality analysis
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()

            # Get earnings data
            earnings_data = financial_api.get_earnings_data(ticker, quarters)

            # Get cash flow data for quality analysis
            cashflow_data = financial_api.get_financial_statements(ticker, "cashflow", "quarterly", quarters)

            result = "# EARNINGS QUALITY ANALYSIS\n\n"

            # Format earnings data
            result += financial_api.format_financial_data(earnings_data, "earnings")

            # Add earnings quality metrics
            result += "\n## Earnings Quality Metrics\n"

            if earnings_data and cashflow_data:
                result += "### Core vs Reported Earnings\n"
                result += "- Analyzing one-time items and exceptional charges\n"
                result += "- Identifying recurring vs non-recurring income\n"
                result += "- Assessing earnings sustainability\n\n"

                result += "### Cash Flow vs Earnings\n"
                result += "- Operating Cash Flow vs Net Income comparison\n"
                result += "- Free Cash Flow generation analysis\n"
                result += "- Working capital impact on earnings\n\n"

                result += "### Revenue Quality\n"
                result += "- Revenue recognition policies\n"
                result += "- Customer concentration analysis\n"
                result += "- Organic vs inorganic growth\n\n"

                result += "### Margin Analysis\n"
                result += "- Gross margin trends and sustainability\n"
                result += "- Operating leverage analysis\n"
                result += "- Cost structure optimization\n"

            return result

        except Exception as e:
            return f"Error retrieving earnings quality analysis: {e}"

    @staticmethod
    @tool
    def get_quarterly_comparison(
        ticker: Annotated[str, "Stock ticker symbol"],
        current_quarter: Annotated[str, "Current quarter to analyze (YYYY-QX format)"],
        comparison_periods: Annotated[int, "Number of previous quarters to compare"] = 4
    ) -> str:
        """
        Compare current quarter with previous quarters and same quarter last year.

        Args:
            ticker: Stock ticker symbol
            current_quarter: Current quarter to analyze (YYYY-QX format)
            comparison_periods: Number of previous quarters to compare

        Returns:
            Detailed quarterly comparison analysis
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()

            # Get quarterly financial data
            income_data = financial_api.get_financial_statements(ticker, "income", "quarterly", comparison_periods + 1)

            result = "# QUARTERLY COMPARISON ANALYSIS\n\n"

            if income_data:
                result += "## Quarter-over-Quarter (QoQ) Analysis\n"
                result += "| Metric | Current Q | Previous Q | QoQ Change | YoY Change |\n"
                result += "|--------|-----------|------------|------------|------------|\n"

                # Add quarterly comparison table
                result += "| Revenue | TBD | TBD | TBD% | TBD% |\n"
                result += "| Gross Profit | TBD | TBD | TBD% | TBD% |\n"
                result += "| Operating Income | TBD | TBD | TBD% | TBD% |\n"
                result += "| Net Income | TBD | TBD | TBD% | TBD% |\n"
                result += "| EPS | TBD | TBD | TBD% | TBD% |\n\n"

                result += "## Seasonal Patterns\n"
                result += "- Identifying seasonal trends in quarterly performance\n"
                result += "- Comparing with historical seasonal patterns\n"
                result += "- Adjusting for seasonal factors\n\n"

                result += "## Segment Performance\n"
                result += "- Business segment breakdown\n"
                result += "- Geographic performance analysis\n"
                result += "- Product line contribution\n\n"

                result += "## Key Performance Indicators\n"
                result += "- Margin expansion/contraction analysis\n"
                result += "- Operational efficiency metrics\n"
                result += "- Return on invested capital trends\n"

            return result

        except Exception as e:
            return f"Error retrieving quarterly comparison: {e}"

    @staticmethod
    @tool
    def get_earnings_estimates_analysis(
        ticker: Annotated[str, "Stock ticker symbol"],
        date: Annotated[str, "Analysis date (YYYY-MM-DD format)"]
    ) -> str:
        """
        Analyze earnings estimates vs actual results and forward guidance.

        Args:
            ticker: Stock ticker symbol
            date: Analysis date (YYYY-MM-DD format)

        Returns:
            Earnings estimates and guidance analysis
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()

            # Get earnings data
            earnings_data = financial_api.get_earnings_data(ticker, 8)

            result = "# EARNINGS ESTIMATES ANALYSIS\n\n"

            if earnings_data:
                result += "## Consensus vs Actual Performance\n"
                result += "| Quarter | Consensus EPS | Actual EPS | Surprise | Surprise % |\n"
                result += "|---------|---------------|------------|----------|------------|\n"

                # Add estimates comparison table
                quarters = earnings_data.get("quarters", [])
                for quarter in quarters[-4:]:  # Last 4 quarters
                    q_name = f"Q{quarter.get('quarter', 'N/A')} {quarter.get('year', 'N/A')}"
                    actual_eps = quarter.get('eps', 'N/A')
                    consensus_eps = quarter.get('consensus_eps', 'N/A')
                    surprise = quarter.get('surprise', 'N/A')
                    surprise_pct = quarter.get('surprise_percent', 'N/A')

                    result += f"| {q_name} | {consensus_eps} | {actual_eps} | {surprise} | {surprise_pct} |\n"

                result += "\n## Estimate Revisions Trends\n"
                result += "- Analyst estimate revisions over time\n"
                result += "- Consensus estimate momentum\n"
                result += "- Estimate dispersion analysis\n\n"

                result += "## Forward Guidance Analysis\n"
                result += "- Management guidance vs street expectations\n"
                result += "- Guidance track record and credibility\n"
                result += "- Forward P/E implications\n\n"

                result += "## Earnings Surprise History\n"
                result += "- Historical beat/miss patterns\n"
                result += "- Magnitude of surprises\n"
                result += "- Market reaction to surprises\n\n"

                result += "## Forward Outlook\n"
                result += "- Next quarter estimates\n"
                result += "- Full year guidance\n"
                result += "- Long-term growth expectations\n"

            return result

        except Exception as e:
            return f"Error retrieving earnings estimates analysis: {e}"
