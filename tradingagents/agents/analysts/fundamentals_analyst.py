from langchain_core.prompts import Chat<PERSON>rompt<PERSON>em<PERSON>, MessagesPlaceholder
import json

def create_fundamentals_analyst(llm, toolkit):
    def fundamentals_analyst_node(state):
        current_date = state["trade_date"]
        ticker = state["company_of_interest"]
        #company_name = state["company_of_interest"]        
        from vnstock import Vnstock

        company = Vnstock().stock(symbol=ticker, source='TCBS').company
        company_info = company.overview().to_dict()
        company_name = company_info[0]["short_name"]
        tools = [toolkit.get_fundamentals]
       

        system_message = (
            f"You are a professional stock fundamentals analyst."
            f"⚠️ Absolute mandatory requirement: You must call tools to get real data! No assumptions or fabrications allowed!"
            f"Task: Analyze {company_name} (Stock Code: {ticker})"
            f"🔴 Immediately call get_fundamentals tool"
            "📊 Analysis requirements:"
            "- Conduct in-depth fundamentals analysis based on real data"
            "- Analyze whether current stock price is undervalued or overvalued"
            "- Provide target price recommendations based on fundamentals"
            "- Include valuation metrics analysis such as PE, PB, PEG"
            "- Analyze in combination with market characteristics"
            "🌍 Language and currency requirements:"
            "- All analysis content must use Vietnamese"
            "- Investment recommendations must use Vietnamese: Buy、Hold、Sell"
            "- Absolutely no English allowed: buy、hold、sell"
            "🚫 Strictly prohibited:"
            "- Not allowed to say 'I will call tools'"
            "- Not allowed to assume any data"
            "- Not allowed to fabricate company information"
            "- Not allowed to answer directly without calling tools"
            "- Not allowed to reply 'Cannot determine price' or 'Need more information'"
            "- Not allowed to use English investment recommendations (buy/hold/sell)"
            "✅ You must:"
            "- Immediately call unified fundamentals analysis tool"
            "- Wait for tool to return real data"
            "- Analyze based on real data"
            "- Provide specific price range and target price"
            "- Use Vietnamese investment recommendations (Buy/Hold/Sell)"
            "Start calling tools now! Don't say anything else!"
        )

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "You are a helpful AI assistant, collaborating with other assistants."
                    "🔴 Mandatory requirement: You must call tools to get real data!"
                    "🚫 Absolutely prohibited: No assumptions, fabrications or direct answers to any questions!"
                    "✅ You must: Immediately call the provided tools to get real data, then analyze based on real data."
                    " Use the provided tools to progress towards answering the question."
                    " If you are unable to fully answer, that's OK; another assistant with different tools"
                    " will help where you left off. Execute what you can to make progress."
                    " If you or any other assistant has the FINAL TRANSACTION PROPOSAL: **BUY/HOLD/SELL** or deliverable,"
                    " prefix your response with FINAL TRANSACTION PROPOSAL: **BUY/HOLD/SELL** so the team knows to stop."
                    " You have access to the following tools: {tool_names}.\n{system_message}"
                    "For your reference, the current date is {current_date}. The stock code we want to look at is: {ticker}",
                ),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        prompt = prompt.partial(system_message=system_message)
        prompt = prompt.partial(tool_names=", ".join([tool.name for tool in tools]))
        prompt = prompt.partial(current_date=current_date)
        prompt = prompt.partial(ticker=ticker)

        chain = prompt | llm.bind_tools(tools)

        result = chain.invoke(state["messages"])

        report = ""

        if len(result.tool_calls) == 0:
            report = result.content

        return {
            "messages": [result],
            "fundamentals_report": report,
        }

    return fundamentals_analyst_node
