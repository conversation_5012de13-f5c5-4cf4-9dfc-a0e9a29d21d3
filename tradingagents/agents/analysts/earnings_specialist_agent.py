from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
import time
import json


def create_earnings_specialist_agent(llm, toolkit):
    """
    Create a specialized earnings analysis agent that focuses on earnings quality,
    quarterly results analysis, and earnings-based investment decisions.
    """

    def earnings_specialist_agent_node(state):
        current_date = state["trade_date"]
        ticker = state["company_of_interest"]
        
        # Tools specific to earnings analysis
        tools = [
            toolkit.get_earnings_quality_analysis,
            toolkit.get_quarterly_comparison,
            toolkit.get_earnings_estimates_analysis,
        ]

        system_message = """Bạn là chuyên gia phân tích earnings và kết quả kinh doanh hàng quý cho thị trường chứng khoán Việt Nam.
        Bạn có chuyên môn sâu về earnings quality, quarterly trends và impact của earnings lên giá cổ phiếu.

        NHIỆM VỤ CỦA BẠN:
        1. Phân tích chất lượng earnings (earnings quality)
        2. So sánh kết quả quarterly với estimates và guidance
        3. Đ<PERSON>h giá sustainability của earnings growth
        4. Phân tích composition của earnings (core vs non-core)
        5. <PERSON><PERSON>h giá management guidance và forward outlook
        6. Dự đoán earnings impact lên stock price

        PHƯƠNG PHÁP PHÂN TÍCH EARNINGS QUALITY:
        - Core Earnings vs Reported Earnings
        - One-time items và exceptional charges
        - Revenue quality và sustainability
        - Cash earnings vs accrual earnings
        - Working capital changes impact
        - Depreciation và amortization policies

        CHỈ SỐ EARNINGS QUAN TRỌNG:
        - EPS Growth (YoY, QoQ)
        - Revenue Growth trends
        - Gross Margin trends
        - Operating Margin expansion/contraction
        - EBITDA Margin analysis
        - Free Cash Flow vs Net Income
        - Earnings Surprise history
        - Guidance vs Actual performance

        QUARTERLY ANALYSIS:
        - Sequential growth (QoQ)
        - Year-over-year comparison (YoY)
        - Seasonal patterns recognition
        - Segment performance breakdown
        - Geographic revenue analysis
        - Product mix changes impact

        EARNINGS ESTIMATES:
        - Consensus estimates vs actual
        - Estimate revisions trends
        - Guidance vs street expectations
        - Forward P/E implications
        - Earnings momentum indicators

        MARKET REACTION ANALYSIS:
        - Historical earnings reaction patterns
        - Post-earnings price movements
        - Volume patterns around earnings
        - Options activity pre/post earnings

        OUTPUT FORMAT:
        1. Earnings Quality Assessment
        2. Quarterly Performance Review
        3. Estimates vs Actual Analysis
        4. Earnings Trends & Sustainability
        5. Management Guidance Evaluation
        6. Market Reaction Prediction
        7. Investment Implications
        8. Risk Factors & Red Flags

        Sử dụng tiếng Việt và đưa ra phân tích chuyên sâu về earnings impact."""

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_message),
            MessagesPlaceholder(variable_name="messages"),
            ("human", f"""Hãy thực hiện phân tích earnings chuyên sâu cho mã cổ phiếu {ticker} tại ngày {current_date}.

            Sử dụng các tools có sẵn để:
            1. Phân tích chất lượng earnings và sustainability
            2. So sánh quarterly results với estimates
            3. Đánh giá earnings trends và forward outlook

            Tập trung vào:
            - Earnings quality và composition
            - Beat/Miss vs consensus estimates
            - Quarterly trends và seasonality
            - Management guidance credibility
            - Earnings sustainability
            - Market reaction prediction

            Đưa ra phân tích khách quan và investment recommendations dựa trên earnings analysis.""")
        ])

        # Create the agent with tools
        agent = prompt | llm.bind_tools(tools)
        
        # Get the response
        response = agent.invoke({"messages": state["messages"]})
        
        return {
            "messages": [response],
            "earnings_analysis_report": response.content,
            "sender": "Earnings Specialist Agent"
        }

    return earnings_specialist_agent_node


def create_earnings_quality_analysis_tool(toolkit):
    """Create specialized tool for earnings quality analysis"""
    
    @toolkit.tool
    def get_earnings_quality_analysis(
        ticker: str,
        quarters: int = 8
    ) -> str:
        """
        Analyze earnings quality and sustainability.
        
        Args:
            ticker: Stock ticker symbol
            quarters: Number of quarters to analyze for trends
            
        Returns:
            Comprehensive earnings quality analysis
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()
            
            # Get earnings data
            earnings_data = financial_api.get_earnings_data(ticker, quarters)
            
            # Get cash flow data for quality analysis
            cashflow_data = financial_api.get_financial_statements(ticker, "cashflow", "quarterly", quarters)
            
            result = "# EARNINGS QUALITY ANALYSIS\n\n"
            
            # Format earnings data
            result += financial_api.format_financial_data(earnings_data, "earnings")
            
            # Add earnings quality metrics
            result += "\n## Earnings Quality Metrics\n"
            
            if earnings_data and cashflow_data:
                result += "### Core vs Reported Earnings\n"
                result += "- Analyzing one-time items and exceptional charges\n"
                result += "- Identifying recurring vs non-recurring income\n"
                result += "- Assessing earnings sustainability\n\n"
                
                result += "### Cash Flow vs Earnings\n"
                result += "- Operating Cash Flow vs Net Income comparison\n"
                result += "- Free Cash Flow generation analysis\n"
                result += "- Working capital impact on earnings\n\n"
                
                result += "### Revenue Quality\n"
                result += "- Revenue recognition policies\n"
                result += "- Customer concentration analysis\n"
                result += "- Organic vs inorganic growth\n\n"
                
                result += "### Margin Analysis\n"
                result += "- Gross margin trends and sustainability\n"
                result += "- Operating leverage analysis\n"
                result += "- Cost structure optimization\n"
            
            return result
                
        except Exception as e:
            return f"Error retrieving earnings quality analysis: {e}"


def create_quarterly_comparison_tool(toolkit):
    """Create specialized tool for quarterly comparison"""
    
    @toolkit.tool
    def get_quarterly_comparison(
        ticker: str,
        current_quarter: str,
        comparison_periods: int = 4
    ) -> str:
        """
        Compare current quarter with previous quarters and same quarter last year.
        
        Args:
            ticker: Stock ticker symbol
            current_quarter: Current quarter to analyze (YYYY-QX format)
            comparison_periods: Number of previous quarters to compare
            
        Returns:
            Detailed quarterly comparison analysis
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()
            
            # Get quarterly financial data
            income_data = financial_api.get_financial_statements(ticker, "income", "quarterly", comparison_periods + 1)
            
            result = "# QUARTERLY COMPARISON ANALYSIS\n\n"
            
            if income_data:
                result += "## Quarter-over-Quarter (QoQ) Analysis\n"
                result += "| Metric | Current Q | Previous Q | QoQ Change | YoY Change |\n"
                result += "|--------|-----------|------------|------------|------------|\n"
                
                # Add quarterly comparison table
                result += "| Revenue | TBD | TBD | TBD% | TBD% |\n"
                result += "| Gross Profit | TBD | TBD | TBD% | TBD% |\n"
                result += "| Operating Income | TBD | TBD | TBD% | TBD% |\n"
                result += "| Net Income | TBD | TBD | TBD% | TBD% |\n"
                result += "| EPS | TBD | TBD | TBD% | TBD% |\n\n"
                
                result += "## Seasonal Patterns\n"
                result += "- Identifying seasonal trends in quarterly performance\n"
                result += "- Comparing with historical seasonal patterns\n"
                result += "- Adjusting for seasonal factors\n\n"
                
                result += "## Segment Performance\n"
                result += "- Business segment breakdown\n"
                result += "- Geographic performance analysis\n"
                result += "- Product line contribution\n\n"
                
                result += "## Key Performance Indicators\n"
                result += "- Margin expansion/contraction analysis\n"
                result += "- Operational efficiency metrics\n"
                result += "- Return on invested capital trends\n"
            
            return result
                
        except Exception as e:
            return f"Error retrieving quarterly comparison: {e}"


def create_earnings_estimates_analysis_tool(toolkit):
    """Create specialized tool for earnings estimates analysis"""
    
    @toolkit.tool
    def get_earnings_estimates_analysis(
        ticker: str,
        date: str
    ) -> str:
        """
        Analyze earnings estimates vs actual results and forward guidance.
        
        Args:
            ticker: Stock ticker symbol
            date: Analysis date (YYYY-MM-DD format)
            
        Returns:
            Earnings estimates and guidance analysis
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()
            
            # Get earnings data
            earnings_data = financial_api.get_earnings_data(ticker, 8)
            
            result = "# EARNINGS ESTIMATES ANALYSIS\n\n"
            
            if earnings_data:
                result += "## Consensus vs Actual Performance\n"
                result += "| Quarter | Consensus EPS | Actual EPS | Surprise | Surprise % |\n"
                result += "|---------|---------------|------------|----------|------------|\n"
                
                # Add estimates comparison table
                quarters = earnings_data.get("quarters", [])
                for quarter in quarters[-4:]:  # Last 4 quarters
                    q_name = f"Q{quarter.get('quarter', 'N/A')} {quarter.get('year', 'N/A')}"
                    actual_eps = quarter.get('eps', 'N/A')
                    consensus_eps = quarter.get('consensus_eps', 'N/A')
                    surprise = quarter.get('surprise', 'N/A')
                    surprise_pct = quarter.get('surprise_percent', 'N/A')
                    
                    result += f"| {q_name} | {consensus_eps} | {actual_eps} | {surprise} | {surprise_pct} |\n"
                
                result += "\n## Estimate Revisions Trends\n"
                result += "- Analyst estimate revisions over time\n"
                result += "- Consensus estimate momentum\n"
                result += "- Estimate dispersion analysis\n\n"
                
                result += "## Forward Guidance Analysis\n"
                result += "- Management guidance vs street expectations\n"
                result += "- Guidance track record and credibility\n"
                result += "- Forward P/E implications\n\n"
                
                result += "## Earnings Surprise History\n"
                result += "- Historical beat/miss patterns\n"
                result += "- Magnitude of surprises\n"
                result += "- Market reaction to surprises\n\n"
                
                result += "## Forward Outlook\n"
                result += "- Next quarter estimates\n"
                result += "- Full year guidance\n"
                result += "- Long-term growth expectations\n"
            
            return result
                
        except Exception as e:
            return f"Error retrieving earnings estimates analysis: {e}"
