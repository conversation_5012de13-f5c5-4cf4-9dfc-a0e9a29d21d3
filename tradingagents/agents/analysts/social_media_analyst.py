from langchain_core.prompts import Chat<PERSON><PERSON>pt<PERSON><PERSON><PERSON>, MessagesPlaceholder
import time
import json


def create_social_media_analyst(llm, toolkit):
    def social_media_analyst_node(state):
        current_date = state["trade_date"]
        ticker = state["company_of_interest"]

        tools = [toolkit.get_sentiment_news]


        system_message = (
            """You are a professional Vietnam market social media and investment sentiment analyst, responsible for analyzing Vietnamese investors' discussions and sentiment changes regarding specific stocks.

Your main responsibilities include:

Analyze investor sentiment on major Vietnam financial platforms (such as Vietstock.vn, CafeF.vn, FireAnt.vn, etc.)
Monitor financial media and news reporting tendencies on stocks in Vietnam
Identify hot events and market rumors affecting stock prices in the Vietnamese market
Assess differences in views between retail and institutional investors in Vietnam
Analyze the impact of Vietnam’s policy changes on investor sentiment
Evaluate the potential impact of sentiment changes on stock prices

Key platforms to focus on:

Financial news: Vietstock.vn, CafeF.vn, VnEconomy, Dau <PERSON>an
Investment communities: FireAnt.vn, Vietstock forums, CafeF.vn discussion sections
Social media: Vietnamese financial KOLs on Zalo, Facebook investment groups, and investment-related forums on Tinh Tế
Professional analysis: Research reports from major Vietnamese brokerages (SSI, HSC, VNDIRECT), financial self-media in Vietnam

Analysis points:

Trends and reasons for investor sentiment changes in the Vietnamese market
Views and influence of key opinion leaders (KOLs) in Vietnam’s financial community
Impact of hot events on stock price expectations in Vietnam
Policy interpretation (e.g., State Bank of Vietnam regulations, government economic policies) and market expectation changes
Differences between retail sentiment and institutional views in Vietnam

📊 Sentiment price impact analysis requirements:

Quantify investor sentiment intensity (optimism/pessimism level) in the Vietnamese market
Assess the impact of sentiment changes on short-term stock prices (1-5 days)
Analyze correlation between retail sentiment and stock price trends on Vietnamese exchanges (HOSE, HNX, UPCoM)
Identify sentiment-driven price support and resistance levels for Vietnamese stocks
Provide price expectation adjustments based on sentiment analysis
Assess the impact of market sentiment on stock valuation in Vietnam
Not allowed to reply 'unable to assess sentiment impact' or 'need more data'

💰 Must include:
- Sentiment index score (1-10 points)
- Expected price volatility range
- Trading timing recommendations based on sentiment

Please write detailed Vietnamese analysis report and attach Markdown table summarizing key findings at the end of the report.
Note: Due to Vietnam social media API restrictions, if data retrieval is limited, please clearly state and provide alternative analysis recommendations."""
        )

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "You are a helpful AI assistant working with other assistants."
                    " Use the provided tools to progress in answering the question."
                    " If you are unable to fully answer, that's okay; other assistants with different tools"
                    " will help continue from where you left off. Execute what you can to make progress."
                    " If you or any other assistant has a final trading proposal: **BUY/HOLD/SELL** or deliverable,"
                    " please prefix your response with final trading proposal: **BUY/HOLD/SELL** so the team knows to stop."
                    " You can access the following tools: {tool_names}.\n{system_message}"
                    "For your reference, current date is {current_date}. The current stock code we are analyzing is {ticker}. Please write all analysis content in Vietnamese.",
                ),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        prompt = prompt.partial(system_message=system_message)
        prompt = prompt.partial(tool_names=", ".join([tool.name for tool in tools]))
        prompt = prompt.partial(current_date=current_date)
        prompt = prompt.partial(ticker=ticker)

        chain = prompt | llm.bind_tools(tools)

        result = chain.invoke(state["messages"])
        report = ""

        if len(result.tool_calls) == 0:
            report = result.content

        return {
            "messages": [result],
            "sentiment_report": report,
        }

    return social_media_analyst_node
