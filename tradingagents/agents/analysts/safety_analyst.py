from langchain_core.tools import tool
from tradingagents.agents.base import Agent
from tradingagents.dataflows.interface import get_fundamentals
from tradingagents.utils.llm_utils import get_llm_config

class SafetyAnalyst(Agent):
    def __init__(self, llm_config=None, **kwargs):
        super().__init__(llm_config, **kwargs)
        self.llm_config = llm_config or get_llm_config("quick_think_llm")
        self.llm = self.llm_config["llm"]
        self.prompt_template = """
        Bạn là một chuyên gia phân tích an toàn tài chính. Nhiệm vụ của bạn là đánh giá "Điểm An toàn Tài chính" cho một cổ phiếu dựa trên dữ liệu cơ bản được cung cấp.
        Điểm an toàn từ 1 (rất rủ<PERSON> ro) đến 10 (r<PERSON><PERSON> an toàn).
        <PERSON><PERSON><PERSON> tập trung vào các yếu tố như nợ, d<PERSON><PERSON> ti<PERSON>, <PERSON><PERSON><PERSON>, và các chỉ số tài ch<PERSON> quan trọng khác.
        Giải thích ngắn gọn lý do cho điểm số.

        Dữ liệu cơ bản của cổ phiếu {ticker}:
        {fundamentals_data}

        Dựa trên dữ liệu trên, hãy đưa ra "Điểm An toàn Tài chính" và giải thích ngắn gọn.
        """

    @tool
    def calculate_safety_score(self, ticker: str, fundamentals_data: str) -> str:
        """
        Tính toán "Điểm An toàn Tài chính" cho một cổ phiếu dựa trên dữ liệu cơ bản.
        Điểm số từ 1 (rất rủi ro) đến 10 (rất an toàn).
        """
        prompt = self.prompt_template.format(ticker=ticker, fundamentals_data=fundamentals_data)
        response = self.llm.invoke(prompt)
        return response.content

    def run(self, state: dict) -> dict:
        ticker = state.get("company_of_interest")
        trade_date = state.get("trade_date")

        # Get fundamentals data (using existing dataflow)
        fundamentals_data = get_fundamentals(ticker, trade_date)

        # Calculate safety score
        safety_analysis = self.calculate_safety_score(ticker, fundamentals_data)

        state["safety_report"] = safety_analysis
        return state

def create_safety_analyst(llm_config=None, **kwargs):
    return SafetyAnalyst(llm_config, **kwargs)
