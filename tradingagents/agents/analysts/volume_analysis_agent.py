from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
import time
import json


def create_volume_analysis_agent(llm, toolkit):
    """
    Create a specialized volume analysis agent that focuses on trading volume patterns,
    order book analysis, and market microstructure insights.
    """

    def volume_analysis_agent_node(state):
        current_date = state["trade_date"]
        ticker = state["company_of_interest"]
        
        # Tools specific to volume and order book analysis
        tools = [
            toolkit.get_volume_analysis,
            toolkit.get_order_book_analysis,
            toolkit.get_trading_patterns,
        ]

        system_message = """Bạn là chuyên gia phân tích volume và microstructure thị trường chứng khoán Việt Nam.
        Bạn có kinh nghiệm sâu rộng trong việc phân tích patterns giao dịch, order book dynamics và market sentiment thông qua volume.

        NHIỆM VỤ CỦA BẠN:
        1. Phân tích volume patterns và trends
        2. Đánh giá order book depth và liquidity
        3. Xác định accumulation/distribution patterns
        4. Phân tích institutional vs retail trading behavior
        5. Đánh giá market sentiment qua volume indicators
        6. Dự đoán price movements dựa trên volume analysis

        PHƯƠNG PHÁP PHÂN TÍCH:
        - Volume Profile Analysis (VPOC, VAH, VAL)
        - Order Flow Analysis (Bid/Ask dynamics)
        - Volume Indicators (OBV, A/D Line, CMF, VWAP)
        - Accumulation/Distribution patterns
        - Volume Breakout/Breakdown signals
        - Market depth và liquidity assessment

        CHỈ SỐ VOLUME QUAN TRỌNG:
        - Volume Moving Averages (10, 20, 50 days)
        - Relative Volume (vs average)
        - Volume Rate of Change
        - On-Balance Volume (OBV)
        - Accumulation/Distribution Line
        - Chaikin Money Flow (CMF)
        - Volume Weighted Average Price (VWAP)
        - Volume Profile (POC, VAH, VAL)

        ORDER BOOK ANALYSIS:
        - Bid/Ask Spread analysis
        - Order book depth và imbalance
        - Large order detection
        - Support/Resistance levels từ order book
        - Market maker vs taker behavior

        TRADING PATTERNS:
        - Volume spikes và breakouts
        - Climax volume patterns
        - Stealth accumulation/distribution
        - Institutional footprints
        - Retail vs smart money flows

        OUTPUT FORMAT:
        1. Volume Overview & Summary
        2. Volume Trend Analysis
        3. Order Book Assessment
        4. Accumulation/Distribution Analysis
        5. Liquidity & Market Depth
        6. Trading Patterns Recognition
        7. Volume-based Signals
        8. Risk Assessment & Recommendations

        Sử dụng tiếng Việt và đưa ra phân tích chuyên sâu về market microstructure."""

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_message),
            MessagesPlaceholder(variable_name="messages"),
            ("human", f"""Hãy thực hiện phân tích volume và order book chuyên sâu cho mã cổ phiếu {ticker} tại ngày {current_date}.

            Sử dụng các tools có sẵn để:
            1. Lấy dữ liệu volume analysis chi tiết
            2. Phân tích order book depth và dynamics
            3. Xác định trading patterns và institutional flows

            Tập trung vào:
            - Volume trends và anomalies
            - Order book imbalances
            - Accumulation/Distribution signals
            - Liquidity assessment
            - Breakout/Breakdown probabilities
            - Market sentiment indicators

            Đưa ra phân tích khách quan và actionable insights cho trading decisions.""")
        ])

        # Create the agent with tools
        agent = prompt | llm.bind_tools(tools)
        
        # Get the response
        response = agent.invoke({"messages": state["messages"]})
        
        return {
            "messages": [response],
            "volume_analysis_report": response.content,
            "sender": "Volume Analysis Agent"
        }

    return volume_analysis_agent_node


def create_volume_analysis_tool(toolkit):
    """Create specialized tool for volume analysis"""
    
    @toolkit.tool
    def get_volume_analysis(
        ticker: str,
        start_date: str,
        end_date: str,
        analysis_type: str = "comprehensive"
    ) -> str:
        """
        Get comprehensive volume analysis data.
        
        Args:
            ticker: Stock ticker symbol
            start_date: Start date for analysis (YYYY-MM-DD)
            end_date: End date for analysis (YYYY-MM-DD)
            analysis_type: Type of analysis ('comprehensive', 'patterns', 'indicators')
            
        Returns:
            Formatted volume analysis data
        """
        try:
            from tradingagents.dataflows.trading_data_api import get_trading_api
            trading_api = get_trading_api()
            
            # Get volume data
            volume_data = trading_api.get_volume_analysis(ticker, start_date, end_date)
            
            if analysis_type == "comprehensive":
                # Get additional market data for comprehensive analysis
                price_data = trading_api.get_historical_prices(ticker, start_date, end_date)
                
                result = "# COMPREHENSIVE VOLUME ANALYSIS\n\n"
                result += trading_api.format_volume_analysis(volume_data)
                result += "\n## Price-Volume Correlation\n"
                result += trading_api.format_price_data(price_data)
                
                return result
            else:
                return trading_api.format_volume_analysis(volume_data)
                
        except Exception as e:
            return f"Error retrieving volume analysis: {e}"


def create_order_book_analysis_tool(toolkit):
    """Create specialized tool for order book analysis"""
    
    @toolkit.tool
    def get_order_book_analysis(
        ticker: str,
        depth: int = 20
    ) -> str:
        """
        Get detailed order book analysis.
        
        Args:
            ticker: Stock ticker symbol
            depth: Order book depth levels to analyze
            
        Returns:
            Formatted order book analysis
        """
        try:
            from tradingagents.dataflows.trading_data_api import get_trading_api
            trading_api = get_trading_api()
            
            order_book_data = trading_api.get_order_book(ticker, depth)
            
            # Format order book analysis
            result = "# ORDER BOOK ANALYSIS\n\n"
            
            if order_book_data:
                bids = order_book_data.get("bids", [])
                asks = order_book_data.get("asks", [])
                
                result += "## Order Book Depth\n"
                result += "| Level | Bid Price | Bid Size | Ask Price | Ask Size | Spread |\n"
                result += "|-------|-----------|----------|-----------|----------|--------|\n"
                
                for i in range(min(len(bids), len(asks), 10)):
                    bid_price = bids[i].get("price", 0) if i < len(bids) else 0
                    bid_size = bids[i].get("size", 0) if i < len(bids) else 0
                    ask_price = asks[i].get("price", 0) if i < len(asks) else 0
                    ask_size = asks[i].get("size", 0) if i < len(asks) else 0
                    spread = ask_price - bid_price if bid_price and ask_price else 0
                    
                    result += f"| {i+1} | {bid_price:.2f} | {bid_size:,.0f} | {ask_price:.2f} | {ask_size:,.0f} | {spread:.2f} |\n"
                
                # Calculate order book metrics
                total_bid_volume = sum(bid.get("size", 0) for bid in bids[:10])
                total_ask_volume = sum(ask.get("size", 0) for ask in asks[:10])
                imbalance = (total_bid_volume - total_ask_volume) / (total_bid_volume + total_ask_volume) if (total_bid_volume + total_ask_volume) > 0 else 0
                
                result += f"\n## Order Book Metrics\n"
                result += f"- Total Bid Volume (Top 10): {total_bid_volume:,.0f}\n"
                result += f"- Total Ask Volume (Top 10): {total_ask_volume:,.0f}\n"
                result += f"- Order Book Imbalance: {imbalance:.2%}\n"
                result += f"- Bid-Ask Spread: {spread:.2f}\n"
                
            return result
                
        except Exception as e:
            return f"Error retrieving order book analysis: {e}"


def create_trading_patterns_tool(toolkit):
    """Create specialized tool for trading patterns analysis"""
    
    @toolkit.tool
    def get_trading_patterns(
        ticker: str,
        start_date: str,
        end_date: str
    ) -> str:
        """
        Identify and analyze trading patterns.
        
        Args:
            ticker: Stock ticker symbol
            start_date: Start date for pattern analysis
            end_date: End date for pattern analysis
            
        Returns:
            Trading patterns analysis
        """
        try:
            from tradingagents.dataflows.trading_data_api import get_trading_api
            trading_api = get_trading_api()
            
            # Get comprehensive market data for pattern analysis
            market_data = trading_api.get_comprehensive_market_data(ticker, start_date, end_date)
            
            result = "# TRADING PATTERNS ANALYSIS\n\n"
            result += market_data
            
            # Add pattern recognition analysis
            result += "\n## Pattern Recognition\n"
            result += "- Volume Breakout Patterns: Analyzing volume spikes above 2x average\n"
            result += "- Accumulation Patterns: Looking for sustained volume with price stability\n"
            result += "- Distribution Patterns: Identifying high volume with price weakness\n"
            result += "- Institutional Footprints: Large block trades and unusual volume\n"
            
            return result
                
        except Exception as e:
            return f"Error retrieving trading patterns: {e}"
