from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
import time
import json


def create_financial_reports_analyst(llm, toolkit):
    """
    Create a specialized financial reports analyst that focuses on deep financial statement analysis.
    This agent leverages the custom financial API to provide comprehensive financial analysis.
    """

    def financial_reports_analyst_node(state):
        current_date = state["trade_date"]
        ticker = state["company_of_interest"]
        
        # Tools specific to financial analysis
        tools = [
            toolkit.get_financial_statements,
            toolkit.get_financial_ratios,
            toolkit.get_earnings_analysis,
        ]

        system_message = """Bạn là chuyên gia phân tích báo cáo tài chính chuyên sâu cho thị trường chứng khoán Việt Nam. 
        Bạn có kinh nghiệm phong phú trong việc phân tích báo cáo tài chính, tính toán các chỉ số tài chính và đánh giá sức khỏe tài chính của doanh nghiệp.

        NHIỆM VỤ CỦA BẠN:
        1. <PERSON><PERSON> tích chi tiết báo cáo tài chính (Balance Sheet, Income Statement, Cash Flow)
        2. T<PERSON>h toán và đánh giá các chỉ số tài chính quan trọng
        3. So sánh với các kỳ trước để xác định xu hướng
        4. Đánh giá chất lượng earnings và tính bền vững
        5. Phân tích cấu trúc vốn và khả năng thanh toán
        6. Đưa ra nhận định về triển vọng tài chính

        PHƯƠNG PHÁP PHÂN TÍCH:
        - Sử dụng các tools để lấy dữ liệu tài chính chi tiết
        - Phân tích trend qua nhiều kỳ (ít nhất 4 quarters)
        - So sánh với industry benchmarks nếu có
        - Tập trung vào quality of earnings
        - Đánh giá red flags và positive signals

        CHỈ SỐ QUAN TRỌNG CẦN PHÂN TÍCH:
        - Profitability: ROE, ROA, Gross Margin, Net Margin
        - Liquidity: Current Ratio, Quick Ratio, Cash Ratio
        - Leverage: Debt/Equity, Interest Coverage, Debt Service Coverage
        - Efficiency: Asset Turnover, Inventory Turnover, Receivables Turnover
        - Growth: Revenue Growth, Earnings Growth, Book Value Growth
        - Valuation: P/E, P/B, EV/EBITDA, PEG

        OUTPUT FORMAT:
        Tạo báo cáo có cấu trúc rõ ràng với:
        1. Executive Summary
        2. Financial Performance Analysis
        3. Financial Position Analysis  
        4. Cash Flow Analysis
        5. Key Financial Ratios
        6. Trend Analysis
        7. Risk Assessment
        8. Investment Recommendation

        Sử dụng tiếng Việt và đưa ra phân tích chuyên sâu, khách quan."""

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_message),
            MessagesPlaceholder(variable_name="messages"),
            ("human", f"""Hãy thực hiện phân tích báo cáo tài chính chuyên sâu cho mã cổ phiếu {ticker} tại ngày {current_date}.

            Sử dụng các tools có sẵn để:
            1. Lấy báo cáo tài chính chi tiết (income statement, balance sheet, cash flow)
            2. Lấy các chỉ số tài chính đã tính toán
            3. Lấy dữ liệu earnings và so sánh với estimates

            Tập trung vào việc đánh giá:
            - Chất lượng và tính bền vững của earnings
            - Sức khỏe tài chính tổng thể
            - Xu hướng phát triển qua các kỳ
            - Rủi ro tài chính tiềm ẩn
            - Triển vọng đầu tư

            Đưa ra phân tích khách quan và recommendation cụ thể.""")
        ])

        # Create the agent with tools
        agent = prompt | llm.bind_tools(tools)
        
        # Get the response
        response = agent.invoke({"messages": state["messages"]})
        
        return {
            "messages": [response],
            "financial_reports_analysis": response.content,
            "sender": "Financial Reports Analyst"
        }

    return financial_reports_analyst_node


def create_financial_statements_tool(toolkit):
    """Create specialized tool for getting detailed financial statements"""
    
    @toolkit.tool
    def get_financial_statements(
        ticker: str,
        statement_type: str = "all",
        periods: int = 4
    ) -> str:
        """
        Get detailed financial statements from the financial API.
        
        Args:
            ticker: Stock ticker symbol
            statement_type: Type of statement ('income', 'balance', 'cashflow', 'all')
            periods: Number of periods to retrieve (default: 4)
            
        Returns:
            Formatted financial statements data
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()
            
            if statement_type == "all":
                # Get all three statements
                income_data = financial_api.get_financial_statements(ticker, "income", "quarterly", periods)
                balance_data = financial_api.get_financial_statements(ticker, "balance", "quarterly", periods)
                cashflow_data = financial_api.get_financial_statements(ticker, "cashflow", "quarterly", periods)
                
                result = "# COMPREHENSIVE FINANCIAL STATEMENTS\n\n"
                result += financial_api.format_financial_data(income_data, "statements")
                result += "\n" + financial_api.format_financial_data(balance_data, "statements")
                result += "\n" + financial_api.format_financial_data(cashflow_data, "statements")
                
                return result
            else:
                data = financial_api.get_financial_statements(ticker, statement_type, "quarterly", periods)
                return financial_api.format_financial_data(data, "statements")
                
        except Exception as e:
            return f"Error retrieving financial statements: {e}"


def create_financial_ratios_tool(toolkit):
    """Create specialized tool for getting financial ratios"""
    
    @toolkit.tool
    def get_financial_ratios(
        ticker: str,
        date: str = None
    ) -> str:
        """
        Get comprehensive financial ratios analysis.
        
        Args:
            ticker: Stock ticker symbol
            date: Specific date for analysis (YYYY-MM-DD format)
            
        Returns:
            Formatted financial ratios data
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()
            
            ratios_data = financial_api.get_financial_ratios(ticker, date)
            return financial_api.format_financial_data(ratios_data, "ratios")
                
        except Exception as e:
            return f"Error retrieving financial ratios: {e}"


def create_earnings_analysis_tool(toolkit):
    """Create specialized tool for earnings analysis"""
    
    @toolkit.tool
    def get_earnings_analysis(
        ticker: str,
        quarters: int = 8
    ) -> str:
        """
        Get detailed earnings analysis and trends.
        
        Args:
            ticker: Stock ticker symbol
            quarters: Number of quarters to analyze (default: 8)
            
        Returns:
            Formatted earnings analysis data
        """
        try:
            from tradingagents.dataflows.financial_reports_api import get_financial_api
            financial_api = get_financial_api()
            
            earnings_data = financial_api.get_earnings_data(ticker, quarters)
            return financial_api.format_financial_data(earnings_data, "earnings")
                
        except Exception as e:
            return f"Error retrieving earnings analysis: {e}"
