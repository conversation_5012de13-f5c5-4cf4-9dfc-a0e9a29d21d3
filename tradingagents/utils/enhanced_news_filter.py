"""
Enhanced News Filter - Integrating Local Small Models and Rule Filtering
Supports multiple filtering strategies: rule filtering, semantic similarity, local classification models
"""

import pandas as pd
import re
import logging
from typing import List, Dict, Tuple, Optional
from datetime import datetime
import numpy as np

# Import base filter
from .news_filter import NewsRelevanceFilter, get_company_name

logger = logging.getLogger(__name__)

class EnhancedNewsFilter(NewsRelevanceFilter):
    """Enhanced news filter, integrating local models and multiple filtering strategies"""

    def __init__(self, stock_code: str, company_name: str, use_semantic: bool = True, use_local_model: bool = False):
        """
        Initialize enhanced filter

        Args:
            stock_code: Stock code
            company_name: Company name
            use_semantic: Whether to use semantic similarity filtering
            use_local_model: Whether to use local classification model
        """
        super().__init__(stock_code, company_name)
        self.use_semantic = use_semantic
        self.use_local_model = use_local_model

        # Semantic model related
        self.sentence_model = None
        self.company_embedding = None

        # Local classification model related
        self.classification_model = None
        self.tokenizer = None

        # Initialize models
        if use_semantic:
            self._init_semantic_model()
        if use_local_model:
            self._init_classification_model()
    
    def _init_semantic_model(self):
        """Initialize semantic similarity model"""
        try:
            logger.info("[Enhanced Filter] Loading semantic similarity model...")

            # Try to use sentence-transformers
            try:
                from sentence_transformers import SentenceTransformer

                # Use lightweight Chinese model
                model_name = "paraphrase-multilingual-MiniLM-L12-v2"  # Lightweight model supporting Chinese
                self.sentence_model = SentenceTransformer(model_name)

                # Pre-compute company-related embeddings
                company_texts = [
                    self.company_name,
                    f"{self.company_name} stock",
                    f"{self.company_name} company",
                    f"{self.stock_code}",
                    f"{self.company_name} performance",
                    f"{self.company_name} financial report"
                ]

                self.company_embedding = self.sentence_model.encode(company_texts)
                logger.info(f"[Enhanced Filter] ✅ Semantic model loaded successfully: {model_name}")

            except ImportError:
                logger.warning("[Enhanced Filter] sentence-transformers not installed, skipping semantic filtering")
                self.use_semantic = False

        except Exception as e:
            logger.error(f"[Enhanced Filter] Semantic model initialization failed: {e}")
            self.use_semantic = False

    def _init_classification_model(self):
        """Initialize local classification model"""
        try:
            logger.info("[Enhanced Filter] Loading local classification model...")

            # Try to use transformers library's Chinese classification model
            try:
                from transformers import AutoTokenizer, AutoModelForSequenceClassification
                import torch

                # Use lightweight Chinese text classification model
                model_name = "uer/roberta-base-finetuned-chinanews-chinese"

                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.classification_model = AutoModelForSequenceClassification.from_pretrained(model_name)

                logger.info(f"[Enhanced Filter] ✅ Classification model loaded successfully: {model_name}")

            except ImportError:
                logger.warning("[Enhanced Filter] transformers not installed, skipping local model classification")
                self.use_local_model = False

        except Exception as e:
            logger.error(f"[Enhanced Filter] Local classification model initialization failed: {e}")
            self.use_local_model = False
    
    def calculate_semantic_similarity(self, title: str, content: str) -> float:
        """
        Calculate semantic similarity score

        Args:
            title: News title
            content: News content

        Returns:
            float: Semantic similarity score (0-100)
        """
        if not self.use_semantic or self.sentence_model is None:
            return 0

        try:
            # Combine title and first 200 characters of content
            text = f"{title} {content[:200]}"

            # Calculate text embedding
            text_embedding = self.sentence_model.encode([text])

            # Calculate similarity with company-related texts
            similarities = []
            for company_emb in self.company_embedding:
                similarity = np.dot(text_embedding[0], company_emb) / (
                    np.linalg.norm(text_embedding[0]) * np.linalg.norm(company_emb)
                )
                similarities.append(similarity)

            # Take highest similarity
            max_similarity = max(similarities)

            # Convert to 0-100 score
            semantic_score = max(0, min(100, max_similarity * 100))

            logger.debug(f"[Enhanced Filter] Semantic similarity score: {semantic_score:.1f}")
            return semantic_score

        except Exception as e:
            logger.error(f"[Enhanced Filter] Semantic similarity calculation failed: {e}")
            return 0
    
    def classify_news_relevance(self, title: str, content: str) -> float:
        """
        Use local model to classify news relevance

        Args:
            title: News title
            content: News content

        Returns:
            float: Classification relevance score (0-100)
        """
        if not self.use_local_model or self.classification_model is None:
            return 0

        try:
            import torch

            # Build classification text
            text = f"{title} {content[:300]}"

            # Add company information as context
            context_text = f"News about {self.company_name}({self.stock_code}): {text}"

            # Tokenize and encode
            inputs = self.tokenizer(
                context_text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )

            # Model inference
            with torch.no_grad():
                outputs = self.classification_model(**inputs)
                logits = outputs.logits

                # Use softmax to get probability distribution
                probabilities = torch.softmax(logits, dim=-1)

                # Assume first category is "relevant", second is "irrelevant"
                # This needs to be adjusted based on specific model
                relevance_prob = probabilities[0][0].item()  # Relevance probability

                # Convert to 0-100 score
                classification_score = relevance_prob * 100

                logger.debug(f"[Enhanced Filter] Classification model score: {classification_score:.1f}")
                return classification_score

        except Exception as e:
            logger.error(f"[Enhanced Filter] Local model classification failed: {e}")
            return 0
    
    def calculate_enhanced_relevance_score(self, title: str, content: str) -> Dict[str, float]:
        """
        Calculate enhanced relevance score (combining multiple methods)

        Args:
            title: News title
            content: News content

        Returns:
            Dict: Dictionary containing various scores
        """
        scores = {}

        # 1. Basic rule score
        rule_score = super().calculate_relevance_score(title, content)
        scores['rule_score'] = rule_score

        # 2. Semantic similarity score
        if self.use_semantic:
            semantic_score = self.calculate_semantic_similarity(title, content)
            scores['semantic_score'] = semantic_score
        else:
            scores['semantic_score'] = 0

        # 3. Local model classification score
        if self.use_local_model:
            classification_score = self.classify_news_relevance(title, content)
            scores['classification_score'] = classification_score
        else:
            scores['classification_score'] = 0

        # 4. Comprehensive score (weighted average)
        weights = {
            'rule': 0.4,      # Rule filtering weight 40%
            'semantic': 0.35,  # Semantic similarity weight 35%
            'classification': 0.25  # Classification model weight 25%
        }

        final_score = (
            weights['rule'] * rule_score +
            weights['semantic'] * scores['semantic_score'] +
            weights['classification'] * scores['classification_score']
        )

        scores['final_score'] = final_score

        logger.debug(f"[Enhanced Filter] Comprehensive score - Rule:{rule_score:.1f}, Semantic:{scores['semantic_score']:.1f}, "
                    f"Classification:{scores['classification_score']:.1f}, Final:{final_score:.1f}")

        return scores
    
    def filter_news_enhanced(self, news_df: pd.DataFrame, min_score: float = 40) -> pd.DataFrame:
        """
        Enhanced news filtering

        Args:
            news_df: Original news DataFrame
            min_score: Minimum comprehensive score threshold

        Returns:
            pd.DataFrame: Filtered news DataFrame with detailed scoring information
        """
        if news_df.empty:
            logger.warning("[Enhanced Filter] Input news DataFrame is empty")
            return news_df

        logger.info(f"[Enhanced Filter] Starting enhanced filtering, original count: {len(news_df)} items, minimum score threshold: {min_score}")

        filtered_news = []

        for idx, row in news_df.iterrows():
            title = row.get('news_title', row.get('title', ''))
            content = row.get('news_content', row.get('content', ''))

            # Calculate enhanced score
            scores = self.calculate_enhanced_relevance_score(title, content)

            if scores['final_score'] >= min_score:
                row_dict = row.to_dict()
                row_dict.update(scores)  # Add all scoring information
                filtered_news.append(row_dict)

                logger.debug(f"[Enhanced Filter] Keeping news (comprehensive score: {scores['final_score']:.1f}): {title[:50]}...")
            else:
                logger.debug(f"[Enhanced Filter] Filtering news (comprehensive score: {scores['final_score']:.1f}): {title[:50]}...")

        # Create filtered DataFrame
        if filtered_news:
            filtered_df = pd.DataFrame(filtered_news)
            # Sort by comprehensive score
            filtered_df = filtered_df.sort_values('final_score', ascending=False)
            logger.info(f"[Enhanced Filter] Enhanced filtering complete, kept {len(filtered_df)} news items")
        else:
            filtered_df = pd.DataFrame()
            logger.warning(f"[Enhanced Filter] All news filtered out, no qualifying news")

        return filtered_df


def create_enhanced_news_filter(ticker: str, use_semantic: bool = True, use_local_model: bool = False) -> EnhancedNewsFilter:
    """
    Convenience function to create enhanced news filter

    Args:
        ticker: Stock code
        use_semantic: Whether to use semantic similarity filtering
        use_local_model: Whether to use local classification model

    Returns:
        EnhancedNewsFilter: Configured enhanced filter instance
    """
    company_name = get_company_name(ticker)
    return EnhancedNewsFilter(ticker, company_name, use_semantic, use_local_model)


# Usage example
if __name__ == "__main__":
    # Test enhanced filter
    import pandas as pd

    # Simulate news data
    test_news = pd.DataFrame([
        {
            'news_title': 'China Merchants Bank releases Q3 2024 performance report',
            'news_content': 'China Merchants Bank released its third quarter financial report today, with net profit growing 8% year-on-year, asset quality continues to improve...'
        },
        {
            'news_title': 'SSE 180 ETF Index Fund (530280) comes with barbell strategy',
            'news_content': 'Data shows that the top ten weighted stocks of the SSE 180 index are Kweichow Moutai, China Merchants Bank 600036...'
        },
        {
            'news_title': 'Bank ETF Index (512730) multiple constituent stocks rise',
            'news_content': 'Banking sector performed strongly today, with multiple constituent stocks including China Merchants Bank and ICBC rising...'
        },
        {
            'news_title': 'China Merchants Bank signs strategic cooperation agreement with tech company',
            'news_content': 'China Merchants Bank announced reaching strategic cooperation with well-known tech company, will cooperate deeply in digital transformation...'
        }
    ])

    print("=== Testing Enhanced News Filter ===")

    # Create enhanced filter (using only rule filtering to avoid model dependencies)
    enhanced_filter = create_enhanced_news_filter('600036', use_semantic=False, use_local_model=False)

    # Filter news
    filtered_news = enhanced_filter.filter_news_enhanced(test_news, min_score=30)

    print(f"Original news: {len(test_news)} items")
    print(f"Filtered news: {len(filtered_news)} items")

    if not filtered_news.empty:
        print("\nFiltered news:")
        for _, row in filtered_news.iterrows():
            print(f"- {row['news_title']} (Comprehensive score: {row['final_score']:.1f})")
            print(f"  Rule score: {row['rule_score']:.1f}, Semantic score: {row['semantic_score']:.1f}, Classification score: {row['classification_score']:.1f}")