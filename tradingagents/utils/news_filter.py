"""
News Relevance Filter
Used to filter news unrelated to specific stocks/companies, improving news analysis quality
"""

import pandas as pd
import re
from typing import List, Dict, Tuple
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class NewsRelevanceFilter:
    """Rule-based news relevance filter"""

    def __init__(self, stock_code: str, company_name: str):
        """
        Initialize filter

        Args:
            stock_code: Stock code, e.g. "600036"
            company_name: Company name, e.g. "China Merchants Bank"
        """
        self.stock_code = stock_code.upper()
        self.company_name = company_name

        # Exclude keywords - these words reduce relevance when present
        self.exclude_keywords = [
            'etf', 'index fund', 'fund', 'index', 'index', 'fund',
            'weighted stock', 'constituent stock', 'sector', 'concept stock', 'theme fund',
            'tracking index', 'passive investment', 'index investment', 'fund holdings'
        ]

        # Include keywords - these words increase relevance when present
        self.include_keywords = [
            'performance', 'financial report', 'announcement', 'restructuring', 'merger', 'dividend', 'dividend payment',
            'executive', 'director', 'shareholder', 'increase holdings', 'reduce holdings', 'buyback',
            'annual report', 'quarterly report', 'semi-annual report', 'performance forecast', 'performance flash report',
            'shareholders meeting', 'board of directors', 'supervisory board', 'major contract',
            'investment', 'acquisition', 'sale', 'transfer', 'cooperation', 'agreement'
        ]

        # Strong relevance keywords - these words significantly increase relevance when present
        self.strong_keywords = [
            'trading halt', 'resume trading', 'limit up', 'limit down', 'restricted shares unlock',
            'equity incentive', 'employee shareholding', 'private placement', 'rights issue', 'bonus shares',
            'asset restructuring', 'backdoor listing', 'delisting', 'ST removal', 'ST'
        ]
    
    def calculate_relevance_score(self, title: str, content: str) -> float:
        """
        Calculate news relevance score

        Args:
            title: News title
            content: News content

        Returns:
            float: Relevance score (0-100)
        """
        score = 0
        title_lower = title.lower()
        content_lower = content.lower()

        # 1. Direct mention of company name
        if self.company_name in title:
            score += 50  # Company name in title, high score
            logger.debug(f"[Filter] Title contains company name '{self.company_name}': +50 points")
        elif self.company_name in content:
            score += 25  # Company name in content, medium score
            logger.debug(f"[Filter] Content contains company name '{self.company_name}': +25 points")

        # 2. Direct mention of stock code
        if self.stock_code in title:
            score += 40  # Stock code in title, high score
            logger.debug(f"[Filter] Title contains stock code '{self.stock_code}': +40 points")
        elif self.stock_code in content:
            score += 20  # Stock code in content, medium score
            logger.debug(f"[Filter] Content contains stock code '{self.stock_code}': +20 points")

        # 3. Strong relevance keyword check
        strong_matches = []
        for keyword in self.strong_keywords:
            if keyword in title_lower:
                score += 30
                strong_matches.append(keyword)
            elif keyword in content_lower:
                score += 15
                strong_matches.append(keyword)

        if strong_matches:
            logger.debug(f"[Filter] Strong relevance keyword matches: {strong_matches}")

        # 4. Include keyword check
        include_matches = []
        for keyword in self.include_keywords:
            if keyword in title_lower:
                score += 15
                include_matches.append(keyword)
            elif keyword in content_lower:
                score += 8
                include_matches.append(keyword)

        if include_matches:
            logger.debug(f"[Filter] Relevant keyword matches: {include_matches[:3]}...")  # Show only first 3

        # 5. Exclude keyword check (deduction)
        exclude_matches = []
        for keyword in self.exclude_keywords:
            if keyword in title_lower:
                score -= 40  # Exclude word in title, major deduction
                exclude_matches.append(keyword)
            elif keyword in content_lower:
                score -= 20  # Exclude word in content, medium deduction
                exclude_matches.append(keyword)

        if exclude_matches:
            logger.debug(f"[Filter] Exclude keyword matches: {exclude_matches[:3]}...")

        # 6. Special rule: if title contains no company info but has exclude words, severe deduction
        if (self.company_name not in title and self.stock_code not in title and
            any(keyword in title_lower for keyword in self.exclude_keywords)):
            score -= 30
            logger.debug(f"[Filter] Title has no company info but contains exclude words: -30 points")

        # Ensure score is within 0-100 range
        final_score = max(0, min(100, score))

        logger.debug(f"[Filter] Final score: {final_score} points - Title: {title[:30]}...")

        return final_score
    
    def filter_news(self, news_df: pd.DataFrame, min_score: float = 30) -> pd.DataFrame:
        """
        Filter news DataFrame

        Args:
            news_df: Original news DataFrame
            min_score: Minimum relevance score threshold

        Returns:
            pd.DataFrame: Filtered news DataFrame, sorted by relevance score
        """
        if news_df.empty:
            logger.warning("[Filter] Input news DataFrame is empty")
            return news_df

        logger.info(f"[Filter] Starting news filtering, original count: {len(news_df)} items, minimum score threshold: {min_score}")

        filtered_news = []

        for idx, row in news_df.iterrows():
            title = row.get('news_title', row.get('title', ''))
            content = row.get('news_content', row.get('content', ''))

            # Calculate relevance score
            score = self.calculate_relevance_score(title, content)

            if score >= min_score:
                row_dict = row.to_dict()
                row_dict['relevance_score'] = score
                filtered_news.append(row_dict)

                logger.debug(f"[Filter] Keeping news (score: {score:.1f}): {title[:50]}...")
            else:
                logger.debug(f"[Filter] Filtering news (score: {score:.1f}): {title[:50]}...")

        # Create filtered DataFrame
        if filtered_news:
            filtered_df = pd.DataFrame(filtered_news)
            # Sort by relevance score
            filtered_df = filtered_df.sort_values('relevance_score', ascending=False)
            logger.info(f"[Filter] Filtering complete, kept {len(filtered_df)} news items")
        else:
            filtered_df = pd.DataFrame()
            logger.warning(f"[Filter] All news filtered out, no qualifying news")

        return filtered_df
    
    def get_filter_statistics(self, original_df: pd.DataFrame, filtered_df: pd.DataFrame) -> Dict:
        """
        Get filtering statistics

        Args:
            original_df: Original news DataFrame
            filtered_df: Filtered news DataFrame

        Returns:
            Dict: Statistics information
        """
        stats = {
            'original_count': len(original_df),
            'filtered_count': len(filtered_df),
            'filter_rate': (len(original_df) - len(filtered_df)) / len(original_df) * 100 if len(original_df) > 0 else 0,
            'avg_score': filtered_df['relevance_score'].mean() if not filtered_df.empty else 0,
            'max_score': filtered_df['relevance_score'].max() if not filtered_df.empty else 0,
            'min_score': filtered_df['relevance_score'].min() if not filtered_df.empty else 0
        }

        return stats


# Stock code to company name mapping
STOCK_COMPANY_MAPPING = {
    # Major Vietnam stocks
    'VNM': 'Vietnam Dairy Products',
    'VIC': 'Vingroup',
    'VCB': 'Vietcombank',
    'HPG': 'Hoa Phat Group',
    'FPT': 'FPT Corporation',
    'SSI': 'SSI Securities Corporation',
    'MSN': 'Masan Group',
    'VHM': 'Vinhomes',
    'GAS': 'PetroVietnam Gas',
    'CTG': 'VietinBank',
    'BID': 'BIDV',
    'TCB': 'Techcombank',

    # Major US stocks
    'AAPL': 'Apple Inc.',
    'MSFT': 'Microsoft Corporation',
    'GOOGL': 'Alphabet Inc.',
    'AMZN': 'Amazon.com Inc.',
    'TSLA': 'Tesla Inc.',
    'META': 'Meta Platforms Inc.',
    'NVDA': 'NVIDIA Corporation',
    'SPY': 'SPDR S&P 500 ETF',

    # More stocks can be added...
}

def get_company_name(ticker: str) -> str:
    """
    Get company name corresponding to stock code

    Args:
        ticker: Stock code

    Returns:
        str: Company name
    """
    # Clean stock code (remove suffix)
    clean_ticker = ticker.split('.')[0]

    company_name = STOCK_COMPANY_MAPPING.get(clean_ticker)

    if company_name:
        logger.debug(f"[Company Mapping] {ticker} -> {company_name}")
        return company_name
    else:
        # If no mapping found, return default name
        default_name = f"Stock {clean_ticker}"
        logger.warning(f"[Company Mapping] No company name mapping found for {ticker}, using default: {default_name}")
        return default_name


def create_news_filter(ticker: str) -> NewsRelevanceFilter:
    """
    Convenience function to create news filter

    Args:
        ticker: Stock code

    Returns:
        NewsRelevanceFilter: Configured filter instance
    """
    company_name = get_company_name(ticker)
    return NewsRelevanceFilter(ticker, company_name)


# Usage example
if __name__ == "__main__":
    # Test filter
    import pandas as pd

    # Simulate news data
    test_news = pd.DataFrame([
        {
            'news_title': 'China Merchants Bank releases Q3 2024 performance report',
            'news_content': 'China Merchants Bank released its third quarter financial report today, with net profit growing 8% year-on-year...'
        },
        {
            'news_title': 'SSE 180 ETF Index Fund (530280) comes with barbell strategy',
            'news_content': 'Data shows that the top ten weighted stocks of the SSE 180 index are Kweichow Moutai, China Merchants Bank 600036...'
        },
        {
            'news_title': 'Bank ETF Index (512730) multiple constituent stocks rise',
            'news_content': 'Banking sector performed strongly today, with China Merchants Bank, ICBC and other constituent stocks rising...'
        }
    ])

    # Create filter
    filter = create_news_filter('600036')

    # Filter news
    filtered_news = filter.filter_news(test_news, min_score=30)

    print(f"Original news: {len(test_news)} items")
    print(f"Filtered news: {len(filtered_news)} items")

    if not filtered_news.empty:
        print("\nFiltered news:")
        for _, row in filtered_news.iterrows():
            print(f"- {row['news_title']} (Score: {row['relevance_score']:.1f})")