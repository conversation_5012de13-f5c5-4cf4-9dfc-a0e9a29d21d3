"""
News Filter Integration Module
Integrates news filters into existing news retrieval processes
"""

import pandas as pd
import logging
from typing import Optional, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

def integrate_news_filtering(original_get_sentiment_news_em):
    """
    Decorator: Add news filtering functionality to get_sentiment_news_em function

    Args:
        original_get_sentiment_news_em: Original get_sentiment_news_em function

    Returns:
        Wrapped function with news filtering functionality
    """
    def filtered_get_sentiment_news_em(symbol: str, enable_filter: bool = True, min_score: float = 30,
                                  use_semantic: bool = False, use_local_model: bool = False) -> pd.DataFrame:
        """
        Enhanced get_sentiment_news_em with integrated news filtering functionality

        Args:
            symbol: Stock code
            enable_filter: Whether to enable news filtering
            min_score: Minimum relevance score threshold
            use_semantic: Whether to use semantic similarity filtering
            use_local_model: Whether to use local classification model

        Returns:
            pd.DataFrame: Filtered news data
        """
        logger.info(f"[News Filter Integration] Starting to get news for {symbol}, filter enabled: {enable_filter}")

        # Call original function to get news
        start_time = datetime.now()
        try:
            news_df = original_get_sentiment_news_em(symbol)
            fetch_time = (datetime.now() - start_time).total_seconds()

            if news_df.empty:
                logger.warning(f"[News Filter Integration] Original function did not retrieve news data for {symbol}")
                return news_df

            logger.info(f"[News Filter Integration] Original news retrieval successful: {len(news_df)} items, time: {fetch_time:.2f}s")

            # If filtering is not enabled, return original data directly
            if not enable_filter:
                logger.info(f"[News Filter Integration] Filtering functionality disabled, returning original news data")
                return news_df

            # Enable news filtering
            filter_start_time = datetime.now()

            try:
                # Import filter
                from tradingagents.utils.enhanced_news_filter import create_enhanced_news_filter

                # Create filter
                news_filter = create_enhanced_news_filter(
                    symbol,
                    use_semantic=use_semantic,
                    use_local_model=use_local_model
                )

                # Execute filtering
                filtered_df = news_filter.filter_news_enhanced(news_df, min_score=min_score)

                filter_time = (datetime.now() - filter_start_time).total_seconds()

                # Record filtering statistics
                original_count = len(news_df)
                filtered_count = len(filtered_df)
                filter_rate = (original_count - filtered_count) / original_count * 100 if original_count > 0 else 0

                logger.info(f"[News Filter Integration] News filtering complete:")
                logger.info(f"  - Original news: {original_count} items")
                logger.info(f"  - Filtered news: {filtered_count} items")
                logger.info(f"  - Filter rate: {filter_rate:.1f}%")
                logger.info(f"  - Filter time: {filter_time:.2f}s")

                if not filtered_df.empty:
                    avg_score = filtered_df['final_score'].mean()
                    max_score = filtered_df['final_score'].max()
                    logger.info(f"  - Average score: {avg_score:.1f}")
                    logger.info(f"  - Highest score: {max_score:.1f}")

                return filtered_df

            except Exception as filter_error:
                logger.error(f"[News Filter Integration] News filtering failed: {filter_error}")
                logger.error(f"[News Filter Integration] Returning original news data as fallback")
                return news_df

        except Exception as fetch_error:
            logger.error(f"[News Filter Integration] Original news retrieval failed: {fetch_error}")
            return pd.DataFrame()  # Return empty DataFrame

    return filtered_get_sentiment_news_em


def patch_akshare_utils():
    """
    Add filtering functionality to akshare_utils module's get_sentiment_news_em function
    """
    try:
        from tradingagents.dataflows import akshare_utils

        # Save original function
        if not hasattr(akshare_utils, '_original_get_sentiment_news_em'):
            akshare_utils._original_get_sentiment_news_em = akshare_utils.get_sentiment_news_em

            # Apply filter decorator
            akshare_utils.get_sentiment_news_em = integrate_news_filtering(
                akshare_utils._original_get_sentiment_news_em
            )

            logger.info("[News Filter Integration] ✅ Successfully added filtering functionality to akshare_utils.get_sentiment_news_em")
        else:
            logger.info("[News Filter Integration] akshare_utils.get_sentiment_news_em has already been enhanced")

    except Exception as e:
        logger.error(f"[News Filter Integration] Unable to enhance akshare_utils.get_sentiment_news_em: {e}")


def create_filtered_realtime_news_function():
    """
    Create enhanced real-time news retrieval function
    """
    def get_filtered_realtime_stock_news(ticker: str, curr_date: str, hours_back: int = 6,
                                       enable_filter: bool = True, min_score: float = 30) -> str:
        """
        Enhanced real-time news retrieval function with integrated news filtering

        Args:
            ticker: Stock code
            curr_date: Current date
            hours_back: Hours to look back
            enable_filter: Whether to enable news filtering
            min_score: Minimum relevance score threshold

        Returns:
            str: Formatted news report
        """
        logger.info(f"[Enhanced Real-time News] Starting to get filtered news for {ticker}")

        try:
            # Import original function
            from tradingagents.dataflows.realtime_news_utils import get_realtime_stock_news

            # Call original function to get news
            original_report = get_realtime_stock_news(ticker, curr_date, hours_back)

            if not enable_filter:
                logger.info(f"[Enhanced Real-time News] Filtering functionality disabled, returning original report")
                return original_report

            # If filtering is enabled and it's A-share, try to re-retrieve and filter
            if any(suffix in ticker for suffix in ['.SH', '.SZ', '.SS', '.XSHE', '.XSHG']) or \
               (not '.' in ticker and ticker.isdigit()):

                logger.info(f"[Enhanced Real-time News] A-share code detected, trying to use filtered East Money news")

                try:
                    from tradingagents.dataflows.akshare_utils import get_sentiment_news_em

                    # Clean stock code
                    clean_ticker = ticker.replace('.SH', '').replace('.SZ', '').replace('.SS', '')\
                                    .replace('.XSHE', '').replace('.XSHG', '')

                    # First get original news
                    original_news_df = get_sentiment_news_em(clean_ticker)

                    if enable_filter and not original_news_df.empty:
                         # Apply news filtering
                         from tradingagents.utils.news_filter import create_news_filter
                         news_filter = create_news_filter(clean_ticker)
                         filtered_news_df = news_filter.filter_news(original_news_df, min_score=min_score)

                         # Record filtering statistics
                         filter_stats = news_filter.get_filter_statistics(original_news_df, filtered_news_df)
                         logger.info(f"[News Filter Integration] News filtering complete:")
                         logger.info(f"  - Original news: {len(original_news_df)} items")
                         logger.info(f"  - Filtered news: {len(filtered_news_df)} items")
                         logger.info(f"  - Filter rate: {filter_stats['filter_rate']:.1f}%")
                    else:
                         filtered_news_df = original_news_df
                    
                    if not filtered_news_df.empty:
                        # Build filtered report
                        news_count = len(filtered_news_df)

                        report = f"# {ticker} Filtered News Report\n\n"
                        report += f"📅 Generation time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                        report += f"📊 Filtered news count: {news_count} items\n"
                        report += f"🔍 Filter threshold: {min_score} points\n\n"

                        # Add filtering statistics
                        if 'final_score' in filtered_news_df.columns:
                            avg_score = filtered_news_df['final_score'].mean()
                            max_score = filtered_news_df['final_score'].max()
                            report += f"📈 Average relevance score: {avg_score:.1f} points\n"
                            report += f"🏆 Highest relevance score: {max_score:.1f} points\n\n"

                        # Add news content
                        for idx, (_, row) in enumerate(filtered_news_df.iterrows()):
                            report += f"### {row.get('news_title', 'No title')}\n"
                            report += f"📅 {row.get('publish_time', 'No time')}\n"

                            if 'final_score' in row:
                                report += f"⭐ Relevance score: {row['final_score']:.1f} points\n"

                            report += f"🔗 {row.get('news_link', 'No link')}\n\n"
                            report += f"{row.get('news_content', 'No content')}\n\n"

                        logger.info(f"[Enhanced Real-time News] ✅ Successfully generated filtered news report with {news_count} high-quality news items")
                        return report
                    else:
                        logger.warning(f"[Enhanced Real-time News] No qualifying news after filtering, returning original report")
                        return original_report

                except Exception as filter_error:
                    logger.error(f"[Enhanced Real-time News] News filtering failed: {filter_error}")
                    return original_report
            else:
                logger.info(f"[Enhanced Real-time News] Non-A-share code, returning original report")
                return original_report

        except Exception as e:
            logger.error(f"[Enhanced Real-time News] Enhanced news retrieval failed: {e}")
            return f"❌ News retrieval failed: {str(e)}"

    return get_filtered_realtime_stock_news


# Auto-apply patches
def apply_news_filtering_patches():
    """
    Automatically apply news filtering patches
    """
    logger.info("[News Filter Integration] Starting to apply news filtering patches...")

    # 1. Enhance akshare_utils
    patch_akshare_utils()

    # 2. Create enhanced real-time news function
    enhanced_function = create_filtered_realtime_news_function()

    logger.info("[News Filter Integration] ✅ News filtering patches applied successfully")

    return enhanced_function


if __name__ == "__main__":
    # Test integration functionality
    print("=== Testing News Filter Integration ===")

    # Apply patches
    enhanced_news_function = apply_news_filtering_patches()

    # Test enhanced function
    test_result = enhanced_news_function(
        ticker="600036",
        curr_date="2024-07-28",
        enable_filter=True,
        min_score=30
    )

    print(f"Test result length: {len(test_result)} characters")
    print(f"Test result preview: {test_result[:200]}...")