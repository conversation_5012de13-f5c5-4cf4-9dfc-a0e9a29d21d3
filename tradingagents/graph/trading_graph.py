# TradingAgents/graph/trading_graph.py

import os
from pathlib import Path
import json
from typing import Dict, Any

from langchain_openai import ChatOpenAI
from langchain_anthropic import <PERSON>tAnthropic
from langchain_google_genai import ChatGoogleGenerativeAI

from langgraph.prebuilt import ToolNode

from tradingagents.agents import *
from tradingagents.default_config import DEFAULT_CONFIG
from tradingagents.agents.utils.memory import FinancialSituationMemory

from tradingagents.dataflows.interface import set_config

from .conditional_logic import ConditionalLogic
from .setup import GraphSetup
from .extended_setup import ExtendedGraphSetup
from .propagation import Propagator
from .reflection import Reflector
from .signal_processing import SignalProcessor


class TradingAgentsGraph:
    """Main class that orchestrates the trading agents framework."""

    def __init__(
        self,
        selected_analysts=["market", "social", "news", "fundamentals"],
        advanced_agents=[],
        use_master_analyst=False,
        debug=False,
        config: Dict[str, Any] = None,
    ):
        """Initialize the trading agents graph and components.

        Args:
            selected_analysts: List of analyst types to include
            advanced_agents: List of advanced agents to include (financial_reports, volume_analysis, earnings_specialist)
            use_master_analyst: Whether to use Master Analyst for intelligence fusion
            debug: Whether to run in debug mode
            config: Configuration dictionary. If None, uses default config
        """
        self.debug = debug
        self.config = config or DEFAULT_CONFIG
        self.selected_analysts = selected_analysts
        self.advanced_agents = advanced_agents
        self.use_master_analyst = use_master_analyst

        # Update the interface's config
        set_config(self.config)

        # Initialize Master Analyst if requested
        if self.use_master_analyst:
            try:
                from tradingagents.intelligence.master_analyst import MasterAnalyst
                self.master_analyst = MasterAnalyst()
                if self.debug:
                    print("✅ Master Analyst initialized for intelligence fusion")
            except ImportError as e:
                print(f"⚠️ Warning: Could not import Master Analyst: {e}")
                self.use_master_analyst = False
                self.master_analyst = None

        # Create necessary directories
        os.makedirs(
            os.path.join(self.config["project_dir"], "dataflows/data_cache"),
            exist_ok=True,
        )

        # Initialize LLMs
        if self.config["llm_provider"].lower() == "openai" or self.config["llm_provider"] == "ollama" or self.config["llm_provider"] == "openrouter":
            self.deep_thinking_llm = ChatOpenAI(model=self.config["deep_think_llm"], base_url=self.config["backend_url"])
            self.quick_thinking_llm = ChatOpenAI(model=self.config["quick_think_llm"], base_url=self.config["backend_url"])
        elif self.config["llm_provider"].lower() == "anthropic":
            self.deep_thinking_llm = ChatAnthropic(model=self.config["deep_think_llm"], base_url=self.config["backend_url"])
            self.quick_thinking_llm = ChatAnthropic(model=self.config["quick_think_llm"], base_url=self.config["backend_url"])
        elif self.config["llm_provider"].lower() == "google":
            self.deep_thinking_llm = ChatGoogleGenerativeAI(model=self.config["deep_think_llm"])
            self.quick_thinking_llm = ChatGoogleGenerativeAI(model=self.config["quick_think_llm"])
        else:
            raise ValueError(f"Unsupported LLM provider: {self.config['llm_provider']}")
        
        self.toolkit = Toolkit(config=self.config)

        # Initialize memories
        self.bull_memory = FinancialSituationMemory("bull_memory", self.config)
        self.bear_memory = FinancialSituationMemory("bear_memory", self.config)
        self.trader_memory = FinancialSituationMemory("trader_memory", self.config)
        self.invest_judge_memory = FinancialSituationMemory("invest_judge_memory", self.config)
        self.risk_manager_memory = FinancialSituationMemory("risk_manager_memory", self.config)

        # Create tool nodes
        self.tool_nodes = self._create_tool_nodes()

        # Initialize components
        self.conditional_logic = ConditionalLogic(
            max_debate_rounds=self.config["max_debate_rounds"],
            max_risk_discuss_rounds=self.config["max_risk_discuss_rounds"]
        )
        # Choose setup based on whether advanced agents are requested
        if advanced_agents:
            self.graph_setup = ExtendedGraphSetup(
                self.quick_thinking_llm,
                self.deep_thinking_llm,
                self.toolkit,
                self.tool_nodes,
                self.bull_memory,
                self.bear_memory,
                self.trader_memory,
                self.invest_judge_memory,
                self.risk_manager_memory,
                self.conditional_logic,
            )
        else:
            self.graph_setup = GraphSetup(
                self.quick_thinking_llm,
                self.deep_thinking_llm,
                self.toolkit,
                self.tool_nodes,
                self.bull_memory,
                self.bear_memory,
                self.trader_memory,
                self.invest_judge_memory,
                self.risk_manager_memory,
                self.conditional_logic,
            )

        self.propagator = Propagator()
        self.reflector = Reflector(self.quick_thinking_llm)
        self.signal_processor = SignalProcessor(self.quick_thinking_llm)

        # State tracking
        self.curr_state = None
        self.ticker = None
        self.log_states_dict = {}  # date to full state dict

        # Set up the graph
        if advanced_agents:
            self.graph = self.graph_setup.setup_extended_graph(selected_analysts, advanced_agents)
        else:
            self.graph = self.graph_setup.setup_graph(selected_analysts)

    def _create_tool_nodes(self) -> Dict[str, ToolNode]:
        """Create tool nodes for different data sources."""
        return {
            "market": ToolNode(
                [
                    self.toolkit.get_YFin_data_online,
                    self.toolkit.get_stockstats_indicators_report_online,
                ]
            ),
            "social": ToolNode(
                [
                    self.toolkit.get_sentiment_news,
                ]
            ),
            "news": ToolNode(
                [
                    self.toolkit.get_google_news,
                    self.toolkit.get_global_news,
                    
                ]
            ),
            "fundamentals": ToolNode(
                [
                    self.toolkit.get_fundamentals,
                ]
            ),
            "safety": ToolNode(
                [
                    self.toolkit.calculate_safety_score,
                ]
            ),
        }

    def _collect_agent_reports(self, state) -> Dict[str, str]:
        """Collect all agent reports from the state for Master Analyst"""
        agent_reports = {}

        # Standard agent reports
        if state.get("market_report"):
            agent_reports["market_analyst"] = state["market_report"]
        if state.get("fundamentals_report"):
            agent_reports["fundamentals_analyst"] = state["fundamentals_report"]
        if state.get("news_report"):
            agent_reports["news_analyst"] = state["news_report"]
        if state.get("sentiment_report"):
            agent_reports["social_analyst"] = state["sentiment_report"]

        # Advanced agent reports (if available)
        if state.get("financial_reports_analysis"):
            agent_reports["financial_reports_analyst"] = state["financial_reports_analysis"]
        if state.get("volume_analysis_report"):
            agent_reports["volume_analysis_agent"] = state["volume_analysis_report"]
        if state.get("earnings_analysis_report"):
            agent_reports["earnings_specialist_agent"] = state["earnings_analysis_report"]

        return agent_reports

    def propagate(self, company_name, trade_date):
        """Run the trading agents graph for a company on a specific date."""

        self.ticker = company_name

        # Initialize state
        init_agent_state = self.propagator.create_initial_state(
            company_name, trade_date
        )
        args = self.propagator.get_graph_args()

        if self.debug:
            # Debug mode with tracing
            trace = []
            for chunk in self.graph.stream(init_agent_state, **args):
                if len(chunk["messages"]) == 0:
                    continue
                
                message = chunk["messages"][-1]
                
                if message.content and message.content.strip():
                    
                    if "FINAL TRANSACTION PROPOSAL:" in message.content:
                        if not hasattr(self, '_final_printed'):
                            message.pretty_print()
                            self._final_printed = True
                    else:
                        message.pretty_print()
                
                trace.append(chunk)

            final_state = trace[-1]
        else:
            # Standard mode without tracing
            final_state = self.graph.invoke(init_agent_state, **args)

        # Use Master Analyst if enabled
        if self.use_master_analyst and self.master_analyst:
            try:
                if self.debug:
                    print("🧠 Running Master Analyst intelligence fusion...")

                # Collect agent reports
                agent_reports = self._collect_agent_reports(final_state)

                if len(agent_reports) >= 2:  # Need at least 2 agents for meaningful analysis
                    # Perform Master Analyst analysis
                    master_result = self.master_analyst.analyze(company_name, agent_reports)

                    # Add Master Analyst results to state
                    final_state["master_analyst_result"] = master_result
                    final_state["master_recommendation"] = master_result.unified_report.final_recommendation.value
                    final_state["master_confidence"] = master_result.unified_report.confidence_score
                    final_state["master_quality_score"] = master_result.quality_metrics.overall_score

                    if self.debug:
                        print(f"✅ Master Analyst completed:")
                        print(f"   Recommendation: {master_result.unified_report.final_recommendation.value.upper()}")
                        print(f"   Confidence: {master_result.unified_report.confidence_score:.1%}")
                        print(f"   Quality: {master_result.quality_metrics.overall_score:.1%}")
                        print(f"   Processing time: {master_result.processing_time:.2f}s")
                else:
                    if self.debug:
                        print(f"⚠️ Insufficient agent reports ({len(agent_reports)}) for Master Analyst")

            except Exception as e:
                print(f"❌ Master Analyst failed: {e}")
                if self.debug:
                    import traceback
                    traceback.print_exc()

        # Store current state for reflection
        self.curr_state = final_state

        # Log state
        self._log_state(trade_date, final_state)

        # Return decision and processed signal
        return final_state, self.process_signal(final_state["final_trade_decision"])

    def _log_state(self, trade_date, final_state):
        """Log the final state to a JSON file."""
        self.log_states_dict[str(trade_date)] = {
            "company_of_interest": final_state["company_of_interest"],
            "trade_date": final_state["trade_date"],
            "market_report": final_state["market_report"],
            "sentiment_report": final_state["sentiment_report"],
            "news_report": final_state["news_report"],
            "fundamentals_report": final_state["fundamentals_report"],
            "investment_debate_state": {
                "bull_history": final_state["investment_debate_state"]["bull_history"],
                "bear_history": final_state["investment_debate_state"]["bear_history"],
                "history": final_state["investment_debate_state"]["history"],
                "current_response": final_state["investment_debate_state"][
                    "current_response"
                ],
                "judge_decision": final_state["investment_debate_state"][
                    "judge_decision"
                ],
            },
            "trader_investment_decision": final_state["trader_investment_plan"],
            "risk_debate_state": {
                "risky_history": final_state["risk_debate_state"]["risky_history"],
                "safe_history": final_state["risk_debate_state"]["safe_history"],
                "neutral_history": final_state["risk_debate_state"]["neutral_history"],
                "history": final_state["risk_debate_state"]["history"],
                "judge_decision": final_state["risk_debate_state"]["judge_decision"],
            },
            "investment_plan": final_state["investment_plan"],
            "final_trade_decision": final_state["final_trade_decision"],
        }

        # Save to file
        directory = Path(f"eval_results/{self.ticker}/TradingAgentsStrategy_logs/")
        directory.mkdir(parents=True, exist_ok=True)

        with open(
            f"eval_results/{self.ticker}/TradingAgentsStrategy_logs/full_states_log_{trade_date}.json",
            "w",
        ) as f:
            json.dump(self.log_states_dict, f, indent=4)

    def reflect_and_remember(self, returns_losses):
        """Reflect on decisions and update memory based on returns."""
        self.reflector.reflect_bull_researcher(
            self.curr_state, returns_losses, self.bull_memory
        )
        self.reflector.reflect_bear_researcher(
            self.curr_state, returns_losses, self.bear_memory
        )
        self.reflector.reflect_trader(
            self.curr_state, returns_losses, self.trader_memory
        )
        self.reflector.reflect_invest_judge(
            self.curr_state, returns_losses, self.invest_judge_memory
        )
        self.reflector.reflect_risk_manager(
            self.curr_state, returns_losses, self.risk_manager_memory
        )

    def process_signal(self, full_signal):
        """Process a signal to extract the core decision."""
        return self.signal_processor.process_signal(full_signal)
