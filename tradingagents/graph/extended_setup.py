# TradingAgents/graph/extended_setup.py

from typing import Dict, Any, List
from langchain_openai import ChatOpenA<PERSON>
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import ToolNode

from tradingagents.agents import *
from tradingagents.agents.utils.agent_states import AgentState
from tradingagents.agents.utils.agent_utils import Toolkit

from .conditional_logic import ConditionalLogic


class ExtendedGraphSetup:
    """Extended setup that includes custom advanced agents."""

    def __init__(
        self,
        quick_thinking_llm: ChatOpenAI,
        deep_thinking_llm: ChatOpenAI,
        toolkit: Toolkit,
        tool_nodes: Dict[str, ToolNode],
        bull_memory,
        bear_memory,
        trader_memory,
        invest_judge_memory,
        risk_manager_memory,
        conditional_logic: ConditionalLogic,
    ):
        """Initialize with required components."""
        self.quick_thinking_llm = quick_thinking_llm
        self.deep_thinking_llm = deep_thinking_llm
        self.toolkit = toolkit
        self.tool_nodes = tool_nodes
        self.bull_memory = bull_memory
        self.bear_memory = bear_memory
        self.trader_memory = trader_memory
        self.invest_judge_memory = invest_judge_memory
        self.risk_manager_memory = risk_manager_memory
        self.conditional_logic = conditional_logic

    def create_extended_tool_nodes(self) -> Dict[str, ToolNode]:
        """Create tool nodes for advanced agents."""
        extended_tools = {
            # Original tool nodes
            **self.tool_nodes,
            
            # Advanced agent tool nodes
            "financial_reports": ToolNode([
                self.toolkit.get_financial_statements,
                self.toolkit.get_financial_ratios,
                self.toolkit.get_earnings_analysis,
            ]),
            "volume_analysis": ToolNode([
                self.toolkit.get_volume_analysis,
                self.toolkit.get_order_book_analysis,
                self.toolkit.get_trading_patterns,
            ]),
            "earnings_specialist": ToolNode([
                self.toolkit.get_earnings_quality_analysis,
                self.toolkit.get_quarterly_comparison,
                self.toolkit.get_earnings_estimates_analysis,
            ]),
        }
        return extended_tools

    def setup_extended_graph(
        self, 
        selected_analysts: List[str] = ["market", "social", "news", "fundamentals"],
        advanced_agents: List[str] = []
    ):
        """
        Set up and compile the extended agent workflow graph.

        Args:
            selected_analysts: List of standard analyst types to include
            advanced_agents: List of advanced agents to include:
                - "financial_reports": Financial Reports Analyst
                - "volume_analysis": Volume Analysis Agent  
                - "earnings_specialist": Earnings Specialist Agent
        """
        if len(selected_analysts) == 0 and len(advanced_agents) == 0:
            raise ValueError("Extended Trading Agents Graph Setup Error: no agents selected!")

        # Create standard analyst nodes
        analyst_nodes = {}
        delete_nodes = {}
        tool_nodes = self.create_extended_tool_nodes()

        # Standard analysts
        if "market" in selected_analysts:
            analyst_nodes["market"] = create_market_analyst(
                self.quick_thinking_llm, self.toolkit
            )
            delete_nodes["market"] = create_msg_delete()

        if "social" in selected_analysts:
            analyst_nodes["social"] = create_social_media_analyst(
                self.quick_thinking_llm, self.toolkit
            )
            delete_nodes["social"] = create_msg_delete()

        if "news" in selected_analysts:
            analyst_nodes["news"] = create_news_analyst(
                self.quick_thinking_llm, self.toolkit
            )
            delete_nodes["news"] = create_msg_delete()

        if "fundamentals" in selected_analysts:
            analyst_nodes["fundamentals"] = create_fundamentals_analyst(
                self.quick_thinking_llm, self.toolkit
            )
            delete_nodes["fundamentals"] = create_msg_delete()

        if "safety" in selected_analysts:
            analyst_nodes["safety"] = create_safety_analyst(
                self.quick_thinking_llm, self.toolkit
            )
            delete_nodes["safety"] = create_msg_delete()

        # Advanced agents
        if "financial_reports" in advanced_agents:
            analyst_nodes["financial_reports"] = create_financial_reports_analyst(
                self.deep_thinking_llm, self.toolkit
            )
            delete_nodes["financial_reports"] = create_msg_delete()

        if "volume_analysis" in advanced_agents:
            analyst_nodes["volume_analysis"] = create_volume_analysis_agent(
                self.quick_thinking_llm, self.toolkit
            )
            delete_nodes["volume_analysis"] = create_msg_delete()

        if "earnings_specialist" in advanced_agents:
            analyst_nodes["earnings_specialist"] = create_earnings_specialist_agent(
                self.deep_thinking_llm, self.toolkit
            )
            delete_nodes["earnings_specialist"] = create_msg_delete()

        # Create researcher nodes
        bull_researcher_node = create_bull_researcher(
            self.deep_thinking_llm, self.bull_memory
        )
        bear_researcher_node = create_bear_researcher(
            self.deep_thinking_llm, self.bear_memory
        )
        research_manager_node = create_research_manager(self.deep_thinking_llm)

        # Create trader node
        trader_node = create_trader(self.deep_thinking_llm, self.trader_memory)

        # Create risk management nodes
        risky_analyst = create_risky_debator(self.quick_thinking_llm)
        neutral_analyst = create_neutral_debator(self.quick_thinking_llm)
        safe_analyst = create_safe_debator(self.quick_thinking_llm)
        risk_manager_node = create_risk_manager(
            self.deep_thinking_llm, self.risk_manager_memory
        )

        # Create workflow
        workflow = StateGraph(AgentState)

        # Add all analyst nodes to the graph
        all_agents = list(selected_analysts) + list(advanced_agents)
        
        for agent_type in all_agents:
            if agent_type in analyst_nodes:
                workflow.add_node(f"{agent_type.capitalize()} Analyst", analyst_nodes[agent_type])
                workflow.add_node(f"Msg Clear {agent_type.capitalize()}", delete_nodes[agent_type])
                workflow.add_node(f"tools_{agent_type}", tool_nodes[agent_type])

        # Add other nodes
        workflow.add_node("Bull Researcher", bull_researcher_node)
        workflow.add_node("Bear Researcher", bear_researcher_node)
        workflow.add_node("Research Manager", research_manager_node)
        workflow.add_node("Trader", trader_node)
        workflow.add_node("Risky Analyst", risky_analyst)
        workflow.add_node("Neutral Analyst", neutral_analyst)
        workflow.add_node("Safe Analyst", safe_analyst)
        workflow.add_node("Risk Judge", risk_manager_node)

        # Define edges - start with first agent
        if all_agents:
            first_agent = all_agents[0]
            workflow.add_edge(START, f"{first_agent.capitalize()} Analyst")

            # Connect agents in sequence
            for i, agent_type in enumerate(all_agents):
                current_analyst = f"{agent_type.capitalize()} Analyst"
                current_tools = f"tools_{agent_type}"
                current_clear = f"Msg Clear {agent_type.capitalize()}"

                # Add conditional edges for current analyst
                workflow.add_conditional_edges(
                    current_analyst,
                    getattr(self.conditional_logic, f"should_continue_{agent_type}", 
                           self.conditional_logic.should_continue_market),  # fallback
                    [current_tools, current_clear],
                )
                workflow.add_edge(current_tools, current_analyst)

                # Connect to next agent or to Bull Researcher if this is the last agent
                if i < len(all_agents) - 1:
                    next_agent = f"{all_agents[i+1].capitalize()} Analyst"
                    workflow.add_edge(current_clear, next_agent)
                elif "safety" in selected_analysts: # If safety analyst is selected, connect fundamentals to safety
                    workflow.add_edge(current_clear, "Safety Analyst")
                else:
                    workflow.add_edge(current_clear, "Bull Researcher")

        # Connect Safety Analyst to Bull Researcher if it exists
        if "safety" in selected_analysts:
            workflow.add_edge("Safety Analyst", "Bull Researcher")

        # Continue with standard workflow
        workflow.add_edge("Bull Researcher", "Bear Researcher")
        workflow.add_edge("Bear Researcher", "Research Manager")

        workflow.add_conditional_edges(
            "Research Manager",
            self.conditional_logic.should_continue_research_manager,
            ["Bull Researcher", "Trader"],
        )

        workflow.add_edge("Trader", "Risky Analyst")
        workflow.add_edge("Risky Analyst", "Neutral Analyst")
        workflow.add_edge("Neutral Analyst", "Safe Analyst")
        workflow.add_edge("Safe Analyst", "Risk Judge")

        workflow.add_conditional_edges(
            "Risk Judge",
            self.conditional_logic.should_continue_risk_judge,
            ["Risky Analyst", END],
        )

        # Compile and return the graph
        return workflow.compile()

    def get_available_advanced_agents(self) -> Dict[str, str]:
        """Get list of available advanced agents with descriptions."""
        return {
            "financial_reports": "Chuyên gia phân tích báo cáo tài chính chi tiết",
            "volume_analysis": "Chuyên gia phân tích volume và order book",
            "earnings_specialist": "Chuyên gia phân tích earnings và quarterly results"
        }
