"""
API connector for macroeconomic data from World Bank and other sources.
Provides access to key economic indicators for Vietnam and other countries.
"""

import requests
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

from .config import get_config


class MacroEconomicAPI:
    """Connector for macroeconomic data APIs"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the macroeconomic data API connector.
        
        Args:
            config: Configuration dictionary containing API settings
        """
        self.config = config or get_config()
        
        # World Bank API settings
        self.world_bank_base_url = "http://api.worldbank.org/v2"
        self.timeout = self.config.get("api_timeout", 30)
        
        # Common headers for requests
        self.headers = {
            'Accept': 'application/json',
            'User-Agent': 'TradingAgents/1.0'
        }
        
        # Economic indicators mapping
        self.indicators = {
            'gdp': 'NY.GDP.MKTP.CD',  # GDP (current US$)
            'gdp_growth': 'NY.GDP.MKTP.KD.ZG',  # GDP growth (annual %)
            'inflation': 'FP.CPI.TOTL.ZG',  # Inflation, consumer prices (annual %)
            'interest_rate': 'FR.INR.RINR',  # Real interest rate (%)
            'unemployment': 'SL.UEM.TOTL.ZS',  # Unemployment, total (% of total labor force)
            'exports': 'NE.EXP.GNFS.CD',  # Exports of goods and services (current US$)
            'imports': 'NE.IMP.GNFS.CD',  # Imports of goods and services (current US$)
            'fdi': 'BX.KLT.DINV.CD.WD',  # Foreign direct investment, net inflows (BoP, current US$)
            'current_account': 'BN.CAB.XOKA.CD',  # Current account balance (BoP, current US$)
            'government_debt': 'GC.DOD.TOTL.GD.ZS',  # Central government debt, total (% of GDP)
        }
    
    def get_indicator_data(
        self, 
        country_code: str, 
        indicator: str, 
        start_year: int, 
        end_year: int
    ) -> List[Dict[str, Any]]:
        """
        Get data for a specific economic indicator.
        
        Args:
            country_code: ISO country code (e.g., 'VN' for Vietnam)
            indicator: World Bank indicator code
            start_year: Start year for data
            end_year: End year for data
            
        Returns:
            List of dictionaries containing indicator data
        """
        endpoint = f"{self.world_bank_base_url}/country/{country_code}/indicator/{indicator}"
        
        params = {
            'date': f"{start_year}:{end_year}",
            'format': 'json',
            'per_page': 100
        }
        
        try:
            response = requests.get(
                endpoint, 
                headers=self.headers, 
                params=params, 
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            # World Bank API returns [metadata, data] format
            if len(data) > 1 and isinstance(data[1], list):
                return [item for item in data[1] if item.get('value') is not None]
            
            return []
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching {indicator} data for {country_code}: {e}")
            return []
    
    def get_gdp_data(
        self, 
        country_code: str = 'VN', 
        start_year: int = 2015, 
        end_year: int = None
    ) -> List[Dict[str, Any]]:
        """Get GDP data for a country."""
        if end_year is None:
            end_year = datetime.now().year - 1
        
        return self.get_indicator_data(country_code, self.indicators['gdp'], start_year, end_year)
    
    def get_gdp_growth_data(
        self, 
        country_code: str = 'VN', 
        start_year: int = 2015, 
        end_year: int = None
    ) -> List[Dict[str, Any]]:
        """Get GDP growth data for a country."""
        if end_year is None:
            end_year = datetime.now().year - 1
        
        return self.get_indicator_data(country_code, self.indicators['gdp_growth'], start_year, end_year)
    
    def get_inflation_data(
        self, 
        country_code: str = 'VN', 
        start_year: int = 2015, 
        end_year: int = None
    ) -> List[Dict[str, Any]]:
        """Get inflation data for a country."""
        if end_year is None:
            end_year = datetime.now().year - 1
        
        return self.get_indicator_data(country_code, self.indicators['inflation'], start_year, end_year)
    
    def get_interest_rate_data(
        self, 
        country_code: str = 'VN', 
        start_year: int = 2015, 
        end_year: int = None
    ) -> List[Dict[str, Any]]:
        """Get interest rate data for a country."""
        if end_year is None:
            end_year = datetime.now().year - 1
        
        return self.get_indicator_data(country_code, self.indicators['interest_rate'], start_year, end_year)
    
    def get_unemployment_data(
        self, 
        country_code: str = 'VN', 
        start_year: int = 2015, 
        end_year: int = None
    ) -> List[Dict[str, Any]]:
        """Get unemployment data for a country."""
        if end_year is None:
            end_year = datetime.now().year - 1
        
        return self.get_indicator_data(country_code, self.indicators['unemployment'], start_year, end_year)
    
    def format_indicator_data(
        self, 
        data: List[Dict[str, Any]], 
        indicator_name: str,
        unit: str = ""
    ) -> str:
        """
        Format indicator data for agent consumption.
        
        Args:
            data: Raw indicator data from API
            indicator_name: Human-readable name of the indicator
            unit: Unit of measurement
            
        Returns:
            Formatted string for agent analysis
        """
        if not data:
            return f"Không có dữ liệu {indicator_name} khả dụng."
        
        formatted_output = f"## {indicator_name}\n\n"
        
        # Sort data by year (most recent first)
        sorted_data = sorted(data, key=lambda x: x.get('date', ''), reverse=True)
        
        formatted_output += "| Năm | Giá trị |\n"
        formatted_output += "|-----|--------|\n"
        
        for item in sorted_data[:10]:  # Show last 10 years
            year = item.get('date', 'N/A')
            value = item.get('value', 'N/A')
            
            if isinstance(value, (int, float)):
                if unit == '%':
                    formatted_output += f"| {year} | {value:.2f}% |\n"
                elif unit == 'USD':
                    formatted_output += f"| {year} | ${value:,.0f} |\n"
                else:
                    formatted_output += f"| {year} | {value:,.2f} {unit} |\n"
            else:
                formatted_output += f"| {year} | {value} |\n"
        
        # Add trend analysis
        if len(sorted_data) >= 2:
            latest = sorted_data[0].get('value')
            previous = sorted_data[1].get('value')
            
            if latest is not None and previous is not None:
                change = latest - previous
                change_pct = (change / previous) * 100 if previous != 0 else 0
                
                formatted_output += f"\n### Phân tích xu hướng\n"
                formatted_output += f"- Giá trị mới nhất ({sorted_data[0].get('date')}): {latest:,.2f} {unit}\n"
                formatted_output += f"- Thay đổi so với năm trước: {change:+.2f} {unit} ({change_pct:+.2f}%)\n"
                
                if change > 0:
                    formatted_output += f"- Xu hướng: Tăng\n"
                elif change < 0:
                    formatted_output += f"- Xu hướng: Giảm\n"
                else:
                    formatted_output += f"- Xu hướng: Ổn định\n"
        
        return formatted_output
    
    def get_comprehensive_macro_data(
        self, 
        country_code: str = 'VN', 
        start_year: int = 2015, 
        end_year: int = None
    ) -> str:
        """
        Get comprehensive macroeconomic data for analysis.
        
        Args:
            country_code: ISO country code (e.g., 'VN' for Vietnam)
            start_year: Start year for data
            end_year: End year for data
            
        Returns:
            Formatted string containing all macroeconomic data
        """
        if end_year is None:
            end_year = datetime.now().year - 1
        
        country_name = "Việt Nam" if country_code == 'VN' else country_code
        output = f"# Báo cáo Kinh tế Vĩ mô - {country_name}\n"
        output += f"Giai đoạn: {start_year} - {end_year}\n\n"
        
        # Get various economic indicators
        gdp_data = self.get_gdp_data(country_code, start_year, end_year)
        gdp_growth_data = self.get_gdp_growth_data(country_code, start_year, end_year)
        inflation_data = self.get_inflation_data(country_code, start_year, end_year)
        interest_rate_data = self.get_interest_rate_data(country_code, start_year, end_year)
        unemployment_data = self.get_unemployment_data(country_code, start_year, end_year)
        
        # Format all data
        output += self.format_indicator_data(gdp_data, "Tổng sản phẩm quốc nội (GDP)", "USD")
        output += "\n" + self.format_indicator_data(gdp_growth_data, "Tăng trưởng GDP", "%")
        output += "\n" + self.format_indicator_data(inflation_data, "Lạm phát", "%")
        output += "\n" + self.format_indicator_data(interest_rate_data, "Lãi suất thực", "%")
        output += "\n" + self.format_indicator_data(unemployment_data, "Tỷ lệ thất nghiệp", "%")
        
        return output


# Singleton instance
_macro_api = None

def get_macro_api() -> MacroEconomicAPI:
    """Get singleton instance of MacroEconomicAPI"""
    global _macro_api
    if _macro_api is None:
        _macro_api = MacroEconomicAPI()
    return _macro_api
