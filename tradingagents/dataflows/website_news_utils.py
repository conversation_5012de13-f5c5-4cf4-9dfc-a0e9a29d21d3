import requests


def getWebsiteNewsData(stock_code, max_news = 50):
    """
    Scrape local news search results for a given query and date range.
    query: str - search query
    max_news: int - maximum number of news to retrieve
    """


    headers = {
        'Accept': '*/*',
        'Accept-Language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin': 'https://vietstock.vn',
        'Referer': 'https://vietstock.vn/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"'
    }


    news_results = []
    stock_code = stock_code.replace(".VN", "")
    url = (
        f"https://dc.vietstock.vn/api/Search/SearchArticleAsync?keySearch={stock_code}&currentPage=1&skip=0&filterTime=all"
        f"&pageSize={max_news}"
    )
    
    try:
        response = requests.post(url, headers=headers)
        data = response.json().get("data")
        for item in data:
            try:
                link = "https://vietstock.vn" + item.get("URL")
                title = item.get("Title")
                snippet = item.get("Content")
                date = item.get("PublishTime")
                source = item.get("Source")
                news_results.append(
                    {
                        "link": link,
                        "title": title,
                        "snippet": snippet,
                        "date": date,
                        "source": source,
                    }
                )
            except Exception as e:
                print(f"Error processing result: {e}")
    except Exception as e:
        print(f"Failed after multiple retries: {e}")
    return news_results
