from typing import Annotated, <PERSON><PERSON>
from .yfin_utils import *
from .stockstats_utils import *
from .googlenews_utils import *
from .website_news_utils import *
from dateutil.relativedelta import relativedelta
from datetime import datetime
import os
import pandas as pd
import yfinance as yf
from .config import get_config, DATA_DIR, set_config
from .search_provider_factory import create_search_provider_factory


def parse_date_range(curr_date: str, look_back_days: int) -> Tuple[str, str]:
    """
    Parse date range and return start and end dates.
    
    Args:
        curr_date: Current date in yyyy-mm-dd format
        look_back_days: Number of days to look back
        
    Returns:
        Tuple of (start_date, end_date) as strings
    """
    end_date = curr_date
    start_date_obj = datetime.strptime(curr_date, "%Y-%m-%d")
    before = start_date_obj - relativedelta(days=look_back_days)
    return before.strftime("%Y-%m-%d"), end_date

def get_google_news(
    query: Annotated[str, "Query to search with"],
    curr_date: Annotated[str, "Curr date in yyyy-mm-dd format"],
    look_back_days: Annotated[int, "how many days to look back"],
) -> str:
    query = query.replace(" ", "+")

    before, _ = parse_date_range(curr_date, look_back_days)
    google_news_results = getNewsData(query, before, curr_date)
    sites_news_results = getWebsiteNewsData(query, 50)
    if len(google_news_results) == 0 and len(sites_news_results) == 0:
        return ""
    news_str = ""
    for news in sites_news_results:
        news_str += (
            f"### {news['title']} (source: {news['source']}) \n\n{news['snippet']}\n\n"
        )
    
    if len(google_news_results) > 0 and len(sites_news_results) < 20:
        for news in google_news_results:
            news_str += (
                f"### {news['title']} (source: {news['source']}) \n\n{news['snippet']}\n\n"
            )
            
    return f"## {query} News, from {before} to {curr_date}:\n\n{news_str}"


def get_stock_stats_indicators_window(
    symbol: Annotated[str, "ticker symbol of the company"],
    indicator: Annotated[str, "technical indicator to get the analysis and report of"],
    curr_date: Annotated[
        str, "The current trading date you are trading on, YYYY-mm-dd"
    ],
    look_back_days: Annotated[int, "how many days to look back"],
    online: Annotated[bool, "to fetch data online or offline"],
) -> str:

    best_ind_params = {
        # Moving Averages
        "close_50_sma": (
            "50 SMA: A medium-term trend indicator. "
            "Usage: Identify trend direction and serve as dynamic support/resistance. "
            "Tips: It lags price; combine with faster indicators for timely signals."
        ),
        "close_200_sma": (
            "200 SMA: A long-term trend benchmark. "
            "Usage: Confirm overall market trend and identify golden/death cross setups. "
            "Tips: It reacts slowly; best for strategic trend confirmation rather than frequent trading entries."
        ),
        "close_10_ema": (
            "10 EMA: A responsive short-term average. "
            "Usage: Capture quick shifts in momentum and potential entry points. "
            "Tips: Prone to noise in choppy markets; use alongside longer averages for filtering false signals."
        ),
        # MACD Related
        "macd": (
            "MACD: Computes momentum via differences of EMAs. "
            "Usage: Look for crossovers and divergence as signals of trend changes. "
            "Tips: Confirm with other indicators in low-volatility or sideways markets."
        ),
        "macds": (
            "MACD Signal: An EMA smoothing of the MACD line. "
            "Usage: Use crossovers with the MACD line to trigger trades. "
            "Tips: Should be part of a broader strategy to avoid false positives."
        ),
        "macdh": (
            "MACD Histogram: Shows the gap between the MACD line and its signal. "
            "Usage: Visualize momentum strength and spot divergence early. "
            "Tips: Can be volatile; complement with additional filters in fast-moving markets."
        ),
        # Momentum Indicators
        "rsi": (
            "RSI: Measures momentum to flag overbought/oversold conditions. "
            "Usage: Apply 70/30 thresholds and watch for divergence to signal reversals. "
            "Tips: In strong trends, RSI may remain extreme; always cross-check with trend analysis."
        ),
        # Volatility Indicators
        "boll": (
            "Bollinger Middle: A 20 SMA serving as the basis for Bollinger Bands. "
            "Usage: Acts as a dynamic benchmark for price movement. "
            "Tips: Combine with the upper and lower bands to effectively spot breakouts or reversals."
        ),
        "boll_ub": (
            "Bollinger Upper Band: Typically 2 standard deviations above the middle line. "
            "Usage: Signals potential overbought conditions and breakout zones. "
            "Tips: Confirm signals with other tools; prices may ride the band in strong trends."
        ),
        "boll_lb": (
            "Bollinger Lower Band: Typically 2 standard deviations below the middle line. "
            "Usage: Indicates potential oversold conditions. "
            "Tips: Use additional analysis to avoid false reversal signals."
        ),
        "atr": (
            "ATR: Averages true range to measure volatility. "
            "Usage: Set stop-loss levels and adjust position sizes based on current market volatility. "
            "Tips: It's a reactive measure, so use it as part of a broader risk management strategy."
        ),
        # Volume-Based Indicators
        "vwma": (
            "VWMA: A moving average weighted by volume. "
            "Usage: Confirm trends by integrating price action with volume data. "
            "Tips: Watch for skewed results from volume spikes; use in combination with other volume analyses."
        ),
        "mfi": (
            "MFI: The Money Flow Index is a momentum indicator that uses both price and volume to measure buying and selling pressure. "
            "Usage: Identify overbought (>80) or oversold (<20) conditions and confirm the strength of trends or reversals. "
            "Tips: Use alongside RSI or MACD to confirm signals; divergence between price and MFI can indicate potential reversals."
        ),
    }

    if indicator not in best_ind_params:
        raise ValueError(
            f"Indicator {indicator} is not supported. Please choose from: {list(best_ind_params.keys())}"
        )

    end_date = curr_date
    before_str, _ = parse_date_range(curr_date, look_back_days)
    before_dt = datetime.strptime(before_str, "%Y-%m-%d")
    curr_date_dt = datetime.strptime(curr_date, "%Y-%m-%d")

    if not online:
        # read from YFin data
        data = pd.read_csv(
            os.path.join(
                DATA_DIR,
                f"market_data/price_data/{symbol}-YFin-data-2015-01-01-2025-03-25.csv",
            )
        )
        data["Date"] = pd.to_datetime(data["Date"], utc=True)
        dates_in_df = data["Date"].astype(str).str[:10]

        ind_string = ""
        while curr_date_dt >= before_dt:
            # only do the trading dates
            if curr_date_dt.strftime("%Y-%m-%d") in dates_in_df.values:
                indicator_value = get_stockstats_indicator(
                    symbol, indicator, curr_date_dt.strftime("%Y-%m-%d"), online
                )

                ind_string += f"{curr_date_dt.strftime('%Y-%m-%d')}: {indicator_value}\n"

            curr_date_dt = curr_date_dt - relativedelta(days=1)
    else:
        # online gathering
        ind_string = ""
        while curr_date_dt >= before_dt:
            indicator_value = get_stockstats_indicator(
                symbol, indicator, curr_date_dt.strftime("%Y-%m-%d"), online
            )

            ind_string += f"{curr_date_dt.strftime('%Y-%m-%d')}: {indicator_value}\n"

            curr_date_dt = curr_date_dt - relativedelta(days=1)

    result_str = (
        f"## {indicator} values from {before_dt.strftime('%Y-%m-%d')} to {end_date}:\n\n"
        + ind_string
        + "\n\n"
        + best_ind_params.get(indicator, "No description available.")
    )

    return result_str


def get_stockstats_indicator(
    symbol: Annotated[str, "ticker symbol of the company"],
    indicator: Annotated[str, "technical indicator to get the analysis and report of"],
    curr_date: Annotated[
        str, "The current trading date you are trading on, YYYY-mm-dd"
    ],
    online: Annotated[bool, "to fetch data online or offline"],
) -> str:

    curr_date_dt = datetime.strptime(curr_date, "%Y-%m-%d")
    curr_date_str = curr_date_dt.strftime("%Y-%m-%d")

    try:
        indicator_value = StockstatsUtils.get_stock_stats(
            symbol,
            indicator,
            curr_date_str
        )
    except Exception as e:
        print(
            f"Error getting stockstats indicator data for indicator {indicator} on {curr_date_str}: {e}"
        )
        return ""

    return str(indicator_value)


def get_YFin_data_window(
    symbol: Annotated[str, "ticker symbol of the company"],
    curr_date: Annotated[str, "Start date in yyyy-mm-dd format"],
    look_back_days: Annotated[int, "how many days to look back"],
) -> str:
    # calculate past days
    start_date, _ = parse_date_range(curr_date, look_back_days)

    # read in data
    data = pd.read_csv(
        os.path.join(
            DATA_DIR,
            f"market_data/price_data/{symbol}-YFin-data-2015-01-01-2025-03-25.csv",
        )
    )

    # Extract just the date part for comparison
    data["DateOnly"] = data["Date"].str[:10]

    # Filter data between the start and end dates (inclusive)
    filtered_data = data[
        (data["DateOnly"] >= start_date) & (data["DateOnly"] <= curr_date)
    ]

    # Drop the temporary column we created
    filtered_data = filtered_data.drop("DateOnly", axis=1)

    # Set pandas display options to show the full DataFrame
    with pd.option_context(
        "display.max_rows", None, "display.max_columns", None, "display.width", None
    ):
        df_string = filtered_data.to_string()

    return (
        f"## Raw Market Data for {symbol} from {start_date} to {curr_date}:\n\n"
        + df_string
    )


def get_YFin_data_online(
    symbol: Annotated[str, "ticker symbol of the company"],
    start_date: Annotated[str, "Start date in yyyy-mm-dd format"],
    end_date: Annotated[str, "End date in yyyy-mm-dd format"],
):
    """
    Get stock price data for a symbol.
    Uses custom trading API if configured, otherwise falls back to Yahoo Finance.
    """
    datetime.strptime(start_date, "%Y-%m-%d")
    datetime.strptime(end_date, "%Y-%m-%d")

    config = get_config()

    # Check if custom trading API is enabled
    if config.get("use_custom_trading_api", False):
        try:
            from .trading_data_api import get_trading_api
            trading_api = get_trading_api()

            # Get comprehensive market data from custom API
            api_result = trading_api.get_comprehensive_market_data(symbol, start_date, end_date)

            if api_result and "No" not in api_result:
                return api_result
            else:
                print(f"Custom trading API returned no data for {symbol}, falling back to Yahoo Finance")
        except Exception as e:
            print(f"Error using custom trading API for {symbol}: {e}, falling back to Yahoo Finance")

    # Fallback to original Yahoo Finance method
    # Create ticker object
    ticker = yf.Ticker(symbol.upper() + ".VN")
    # Fetch historical data for the specified date range
    data = ticker.history(start=start_date, end=end_date)
    # Check if data is empty
    if data.empty:
        return (
            f"No data found for symbol '{symbol}' between {start_date} and {end_date}"
        )

    # Remove timezone info from index for cleaner output
    if data.index.tz is not None:
        data.index = data.index.tz_localize(None)

    # Round numerical values to 2 decimal places for cleaner display
    numeric_columns = ["Open", "High", "Low", "Close", "Adj Close"]
    for col in numeric_columns:
        if col in data.columns:
            data[col] = data[col].round(2)

    # Convert DataFrame to CSV string
    csv_string = data.to_csv()

    # Add header information
    header = f"# Stock data for {symbol.upper()} from {start_date} to {end_date}\n"
    header += f"# Total records: {len(data)}\n"
    header += f"# Data retrieved on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

    return header + csv_string

# Enhanced search provider factory instance (singleton)
_search_factory = create_search_provider_factory()


def get_sentiment_news(ticker, curr_date):
    config = get_config()
    search_provider = _search_factory.create_provider(config)
    query = f"Search social media for stock code \"{ticker}\" in Vietnam from 14 days before {curr_date} to {curr_date}. Ensure the data is posted only within that period and relevant to Vietnam."
    return search_provider.search(query)
    

def get_global_news(curr_date):
    config = get_config()
    search_provider = _search_factory.create_provider(config)
    query = f"Search for global macroeconomic news and financial market updates from 14 days before {curr_date} to {curr_date}, today is {curr_date}. Focus on central bank decisions, economic indicators, geopolitical events, and market-moving news that would be important for trading decisions. Ensure the data is posted only within that period and relevant to Vietnam."
    return search_provider.search(query)
    

def get_fundamentals(ticker, curr_date):
    """
    Get fundamental analysis data for a stock.
    Uses custom API if configured, otherwise falls back to web search.
    """
    config = get_config()

    # Check if custom financial API is enabled
    if config.get("use_custom_financial_api", False):
        try:
            from .financial_reports_api import get_financial_api
            financial_api = get_financial_api()

            # Get comprehensive fundamental data from custom API
            api_result = financial_api.get_comprehensive_fundamentals(ticker, curr_date)

            if api_result and "No" not in api_result:
                return api_result
            else:
                print(f"Custom financial API returned no data for {ticker}, falling back to web search")
        except Exception as e:
            print(f"Error using custom financial API for {ticker}: {e}, falling back to web search")

    # Fallback to original web search method
    search_provider = _search_factory.create_provider(config)
    query = f"Search for fundamental analysis data and financial metrics for {ticker} stock listed in Vietnam from the month before {curr_date} to the month of {curr_date}, today is {curr_date}. Look for earnings reports, financial ratios such as P/E, P/S, cash flow, revenue growth, analyst ratings, and any fundamental analysis discussions relevant to the Vietnamese market. Please present key metrics in a structured format."
    return search_provider.search(query)
    
