"""
API connector for trading/market data.
Replace placeholder values with your actual API details.
"""

import requests
from typing import Dict, Any

from tradingagents.dataflows.utils import convert_timestamp_to_date
from .config import get_config


class TradingDataAPI:
    """Connector for trading/market data API"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the trading data API connector.
        
        Args:
            config: Configuration dictionary containing API settings
        """
        self.config = config or get_config()
        
        self.headers = {
        'Accept': '*/*',
        'Accept-Language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'Origin': 'https://chart.vps.com.vn',
        'Referer': 'https://chart.vps.com.vn/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"'
        }
        self.base_url = self.config.get("trading_api_base_url")
        self.timeout = self.config.get("api_timeout", 30)
        
    
    def get_historical_prices(
        self, 
        symbol: str, 
        start_date: str, 
        end_date: str,
        interval: str = "1d"
    ) -> Dict[str, Any]:
        """
        Get historical price data for a symbol.
        
        Args:
            symbol: Stock symbol
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            interval: Data interval  ('1d')
            
        Returns:
            Dictionary containing historical price data
        """
        # TODO: Replace with your actual API endpoint
        endpoint = f"{self.base_url}/{symbol.upper()}"
        
        params = {
            "pageSize": 100,

        }
        
        try:
            response = requests.get(
                endpoint, 
                headers=self.headers, 
                params=params, 
                timeout=self.timeout
            )
            response.raise_for_status()
            
            return response.json()["data"]["list"]
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching historical data for {symbol}: {e}")
            return {}
    
    def get_real_time_quote(self, symbol: str) -> Dict[str, Any]:
        """
        Get real-time quote for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary containing real-time quote data
        """
        # TODO: Replace with your actual API endpoint
        endpoint = f"{self.base_url}/quote"
        
        params = {
            "symbol": symbol.upper()
        }
        
        try:
            response = requests.get(
                endpoint, 
                headers=self.headers, 
                params=params, 
                timeout=self.timeout
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching real-time quote for {symbol}: {e}")
            return {}
    
    def get_volume_analysis(
        self, 
        symbol: str, 
        start_date: str, 
        end_date: str
    ) -> Dict[str, Any]:
        """
        Get volume analysis data.
        
        Args:
            symbol: Stock symbol
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            
        Returns:
            Dictionary containing volume analysis data
        """
        # TODO: Replace with your actual API endpoint
        endpoint = f"{self.base_url}/volume"
        
        params = {
            "symbol": symbol.upper(),
            "start_date": start_date,
            "end_date": end_date
        }
        
        try:
            response = requests.get(
                endpoint, 
                headers=self.headers, 
                params=params, 
                timeout=self.timeout
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching volume analysis for {symbol}: {e}")
            return {}
    
    def get_order_book(self, symbol: str, depth: int = 10) -> Dict[str, Any]:
        """
        Get order book data.
        
        Args:
            symbol: Stock symbol
            depth: Order book depth (number of levels)
            
        Returns:
            Dictionary containing order book data
        """
        # TODO: Replace with your actual API endpoint
        endpoint = f"{self.base_url}/orderbook"
        
        params = {
            "symbol": symbol.upper(),
            "depth": depth
        }
        
        try:
            response = requests.get(
                endpoint, 
                headers=self.headers, 
                params=params, 
                timeout=self.timeout
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching order book for {symbol}: {e}")
            return {}
    
    def format_price_data(self, prices: Dict[str, Any]) -> str:
        """
        Format price data for agent consumption.
        
        Args:
            prices: Raw price data from API
            
        Returns:
            Formatted string for agent analysis
        """
        if not prices:
            return "No price data available."
        
        formatted_output = "## Historical Price Data\n\n"
        
        try:
            # TODO: Adjust based on your API response structure
            
            if prices:
                formatted_output += "| Ngày | Giá mở cửa | Giá cao nhất | Giá thấp nhất | Giá đóng cửa | Khối lượng mua | Số lượng mua | Khối lượng bán | Số lượng bán | Khối lượng giao dịch khớp lệnh | Giá trị giao dịch khớp lệnh |\n"
                formatted_output += "|------|------------|--------------|---------------|---------------|---------------|--------------|----------------|--------------|--------------------------------|-----------------------------|\n"
                
                for price in prices[-10:]:  # Show last 10 days
                    date = convert_timestamp_to_date(price.get("reportDate"))
                    open_price = price.get("openPrice", 0)
                    high_price = price.get("highPrice", 0)
                    low_price = price.get("lowPrice", 0)
                    close_price = price.get("closePrice", 0)
                    volume_buy = price.get("buyingVolume", 0)
                    quantity_buy = price.get("buyingOrder", 0)
                    volume_sell = price.get("sellingVolume", 0)
                    quantity_sell = price.get("sellingOrder", 0)
                    volume_match = price.get("mainVolume", 0)
                    value_match = price.get("mainValue", 0)

                    formatted_output += f"| {date} | {open_price:.2f} | {high_price:.2f} | {low_price:.2f} | {close_price:.2f} | {volume_buy:,.0f} | {quantity_buy:,.0f} | {volume_sell:,.0f} | {quantity_sell:,.0f} | {volume_match:,.0f} | {value_match:,.0f} |\n"
                
                # Add summary statistics
                latest = prices[-1] if prices else {}
                formatted_output += f"\n### Latest Quote\n"
                formatted_output += f"- Current Price: {latest.get('closePrice', 'N/A')}\n"
                formatted_output += f"- Volume: {latest.get('mainVolume', 'N/A'):,.0f}\n"
                formatted_output += f"- Value: {latest.get('mainValue', 'N/A'):,.0f}\n"
                
        except Exception as e:
            formatted_output += f"Error formatting price data: {e}\n"
        
        return formatted_output
    
    def format_volume_analysis(self, prices: Dict[str, Any]) -> str:
        """
        Format volume analysis data.
        
        Args:
            prices: Raw volume data from API
            
        Returns:
            Formatted string for agent analysis
        """
        if not prices:
            return "No volume analysis data available."
        
        formatted_output = "## Volume Analysis\n\n"
        
        try:
            i = 0
            output = dict({
                "buyingVolume": 0,
                "buyingOrder": 0,
                "sellingVolume": 0,
                "sellingOrder": 0,
                "mainVolume": 0,
                "mainValue": 0
            })

            for price in prices:
                output["buyingVolume"] += price.get("buyingVolume", 0)
                output["buyingOrder"] += price.get("buyingOrder", 0)
                output["sellingVolume"] += price.get("sellingVolume", 0)
                output["sellingOrder"] += price.get("sellingOrder", 0)
                output["mainVolume"] += price.get("mainVolume", 0)
                output["mainValue"] += price.get("mainValue", 0)
                if i > 30:
                    break
                i += 1
            formatted_output += f"- Khối lượng mua trung bình: {output['buyingVolume'] / 30:,.0f}\n"
            formatted_output += f"- Số lượng mua trung bình: {output['buyingOrder'] / 30:,.0f}\n"
            formatted_output += f"- Khối lượng bán trung bình: {output['sellingVolume'] / 30:,.0f}\n"
            formatted_output += f"- Số lượng bán trung bình: {output['sellingOrder'] / 30:,.0f}\n"
            formatted_output += f"- Khối lượng giao dịch trung bình: {output['mainVolume'] / 30:,.0f}\n"
            formatted_output += f"- Giá trị giao dịch trung bình: {output['mainValue'] / 30:,.0f}\n"
            
        except Exception as e:
            formatted_output += f"Error formatting volume analysis: {e}\n"
        
        return formatted_output
    
    def get_comprehensive_market_data(
        self, 
        symbol: str, 
        start_date: str, 
        end_date: str
    ) -> str:
        """
        Get comprehensive market data for analysis.
        
        Args:
            symbol: Stock symbol
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            
        Returns:
            Formatted string containing all market data
        """
        output = f"# Comprehensive Market Data for {symbol.upper()}\n"
        output += f"Period: {start_date} to {end_date}\n\n"
        
        # Get various data types
        price_data = self.get_historical_prices(symbol, start_date, end_date)
        #volume_data = self.get_volume_analysis(symbol, start_date, end_date)
        #real_time_data = self.get_real_time_quote(symbol)
        real_time_data = None
        # Format all data
        output += self.format_price_data(price_data)
        output += "\n" + self.format_volume_analysis(price_data)
        
        # Add real-time quote if available
        if real_time_data:
            output += "\n## Real-time Quote\n"
            output += f"- Last Price: {real_time_data.get('last_price', 'N/A')}\n"
            output += f"- Bid: {real_time_data.get('bid', 'N/A')}\n"
            output += f"- Ask: {real_time_data.get('ask', 'N/A')}\n"
            output += f"- Last Update: {real_time_data.get('timestamp', 'N/A')}\n"
        
        return output


# Singleton instance
_trading_api = None

def get_trading_api() -> TradingDataAPI:
    """Get singleton instance of TradingDataAPI"""
    global _trading_api
    if _trading_api is None:
        _trading_api = TradingDataAPI()
    return _trading_api
