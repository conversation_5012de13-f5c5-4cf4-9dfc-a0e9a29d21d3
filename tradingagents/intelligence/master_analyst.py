"""
Master Analyst - Intelligence Fusion Component
Synthesizes all agent outputs into a unified decision framework.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import time

from .data_fusion import DataFusionStrategy, DataPoint, FusedData
from .cross_validation import CrossValidator, CrossValidationReport
from .weighted_analysis import WeightedAnalysisEngine, WeightedAnalysis
from .conflict_resolution import ConflictResolutionEngine, Conflict, Resolution
from .unified_output import UnifiedReportGenerator, UnifiedReport
from .quality_scoring import QualityScoringEngine, QualityMetrics, ConfidenceBreakdown


@dataclass
class MasterAnalysisResult:
    """Complete result from Master Analyst"""
    # Core Results
    unified_report: UnifiedReport
    quality_metrics: QualityMetrics
    confidence_breakdown: ConfidenceBreakdown
    
    # Intermediate Results
    fused_data: List[FusedData]
    validation_report: CrossValidationReport
    weighted_analysis: WeightedAnalysis
    conflicts: List[Conflict]
    resolutions: Dict[str, Resolution]
    
    # Processing Metadata
    processing_time: float
    timestamp: datetime
    
    # Summary
    executive_summary: str
    actionable_insights: List[str]
    next_steps: List[str]


class MasterAnalyst:
    """
    Master Analyst - The Intelligence Fusion Component
    
    Orchestrates the entire intelligence pipeline:
    1. Data Fusion from multiple agents
    2. Cross-validation and conflict detection
    3. Weighted analysis with dynamic scoring
    4. Conflict resolution
    5. Quality assessment
    6. Unified report generation
    """
    
    def __init__(self):
        """Initialize the Master Analyst with all intelligence components"""
        self.data_fusion = DataFusionStrategy()
        self.cross_validator = CrossValidator()
        self.weighted_analyzer = WeightedAnalysisEngine()
        self.conflict_resolver = ConflictResolutionEngine()
        self.report_generator = UnifiedReportGenerator()
        self.quality_scorer = QualityScoringEngine()
        
        # Performance tracking
        self.analysis_history = []
        self.performance_metrics = {}
    
    def analyze(
        self,
        symbol: str,
        agent_reports: Dict[str, str],
        metadata: Optional[Dict[str, Any]] = None
    ) -> MasterAnalysisResult:
        """
        Perform comprehensive analysis using all intelligence components.
        
        Args:
            symbol: Stock symbol being analyzed
            agent_reports: Dictionary mapping agent names to their reports
            metadata: Optional metadata about the analysis context
            
        Returns:
            Complete Master Analysis Result
        """
        start_time = time.time()
        
        try:
            # Step 1: Data Fusion
            print(f"🔄 Master Analyst: Starting analysis for {symbol}")
            print("📊 Step 1: Data Fusion...")
            data_points = self.data_fusion.extract_data_points(agent_reports)
            fused_data = self.data_fusion.fuse_data_points(data_points)
            print(f"   ✅ Fused {len(data_points)} data points into {len(fused_data)} consolidated metrics")
            
            # Step 2: Cross-Validation
            print("🔍 Step 2: Cross-Validation...")
            validation_report = self.cross_validator.validate_data_consistency(data_points)
            print(f"   ✅ Validation completed: {validation_report.overall_result.value}")
            print(f"   📈 Consistency Score: {validation_report.consistency_score:.1%}")
            
            # Step 3: Conflict Detection
            print("⚠️  Step 3: Conflict Detection...")
            conflicts = self.conflict_resolver.detect_conflicts(fused_data, validation_report)
            print(f"   ✅ Detected {len(conflicts)} conflicts")
            
            # Step 4: Weighted Analysis
            print("⚖️  Step 4: Weighted Analysis...")
            dynamic_weights = self.weighted_analyzer.calculate_dynamic_weights(data_points, validation_report)
            weighted_analysis = self.weighted_analyzer.perform_weighted_analysis(
                fused_data, validation_report, dynamic_weights
            )
            print(f"   ✅ Analysis completed: {weighted_analysis.final_recommendation}")
            print(f"   🎯 Confidence: {weighted_analysis.confidence_score:.1%}")
            
            # Step 5: Conflict Resolution
            print("🔧 Step 5: Conflict Resolution...")
            resolutions = self.conflict_resolver.resolve_conflicts(conflicts, fused_data, dynamic_weights)
            print(f"   ✅ Resolved {len(resolutions)} conflicts")
            
            # Step 6: Quality Assessment
            print("📋 Step 6: Quality Assessment...")
            quality_metrics = self.quality_scorer.calculate_quality_metrics(
                fused_data, validation_report, weighted_analysis, conflicts, resolutions
            )
            confidence_breakdown = self.quality_scorer.generate_confidence_breakdown(
                quality_metrics, weighted_analysis
            )
            print(f"   ✅ Quality Score: {quality_metrics.overall_score:.1%}")
            print(f"   🔒 Confidence Level: {quality_metrics.confidence_level.value}")
            
            # Step 7: Report Generation
            print("📄 Step 7: Report Generation...")
            processing_time = time.time() - start_time
            unified_report = self.report_generator.generate_unified_report(
                symbol, fused_data, validation_report, weighted_analysis,
                conflicts, resolutions, processing_time
            )
            print(f"   ✅ Unified report generated")
            
            # Step 8: Generate Insights
            print("💡 Step 8: Generating Insights...")
            executive_summary = self._generate_executive_summary(
                unified_report, quality_metrics, weighted_analysis
            )
            actionable_insights = self._generate_actionable_insights(
                unified_report, quality_metrics, conflicts, resolutions
            )
            next_steps = self._generate_next_steps(
                unified_report, quality_metrics, validation_report
            )
            
            # Create final result
            result = MasterAnalysisResult(
                unified_report=unified_report,
                quality_metrics=quality_metrics,
                confidence_breakdown=confidence_breakdown,
                fused_data=fused_data,
                validation_report=validation_report,
                weighted_analysis=weighted_analysis,
                conflicts=conflicts,
                resolutions=resolutions,
                processing_time=processing_time,
                timestamp=datetime.now(),
                executive_summary=executive_summary,
                actionable_insights=actionable_insights,
                next_steps=next_steps
            )
            
            # Track performance
            self._track_analysis_performance(result)
            
            print(f"✅ Master Analysis completed in {processing_time:.2f}s")
            print(f"🎯 Final Recommendation: {unified_report.final_recommendation.value.upper()}")
            print(f"📊 Overall Confidence: {unified_report.confidence_score:.1%}")
            
            return result
            
        except Exception as e:
            print(f"❌ Master Analysis failed: {e}")
            raise
    
    def _generate_executive_summary(
        self,
        report: UnifiedReport,
        quality_metrics: QualityMetrics,
        weighted_analysis: WeightedAnalysis
    ) -> str:
        """Generate executive summary of the analysis"""
        
        summary_parts = []
        
        # Recommendation summary
        summary_parts.append(
            f"**Investment Recommendation for {report.symbol}: {report.final_recommendation.value.upper()}**"
        )
        
        # Confidence and quality
        summary_parts.append(
            f"Analysis Confidence: {report.confidence_score:.0%} | "
            f"Data Quality: {quality_metrics.overall_score:.0%} | "
            f"Risk Level: {report.overall_risk_level}"
        )
        
        # Key drivers
        if weighted_analysis.contributing_factors:
            summary_parts.append("**Key Supporting Factors:**")
            for factor in weighted_analysis.contributing_factors[:3]:  # Top 3
                summary_parts.append(f"• {factor}")
        
        # Risk highlights
        if report.risk_factors:
            high_risk_factors = [rf for rf in report.risk_factors if rf.severity in ['high', 'critical']]
            if high_risk_factors:
                summary_parts.append("**Key Risk Factors:**")
                for risk in high_risk_factors[:2]:  # Top 2
                    summary_parts.append(f"• {risk.description}")
        
        # Data quality note
        if quality_metrics.overall_score < 0.7:
            summary_parts.append("⚠️ **Note:** Analysis based on limited or inconsistent data - exercise additional caution")
        elif quality_metrics.overall_score > 0.9:
            summary_parts.append("✅ **Note:** High-quality analysis with strong data foundation")
        
        return "\n".join(summary_parts)
    
    def _generate_actionable_insights(
        self,
        report: UnifiedReport,
        quality_metrics: QualityMetrics,
        conflicts: List[Conflict],
        resolutions: Dict[str, Resolution]
    ) -> List[str]:
        """Generate actionable insights based on the analysis"""
        insights = []
        
        # Recommendation-specific insights
        if report.final_recommendation.value in ['strong_buy', 'buy']:
            insights.append("Consider position sizing based on confidence level and risk tolerance")
            insights.append("Monitor key support levels for entry timing")
        elif report.final_recommendation.value in ['strong_sell', 'sell']:
            insights.append("Consider exit strategy and timing based on market conditions")
            insights.append("Review portfolio impact before executing sell decision")
        else:  # HOLD
            insights.append("Maintain current position while monitoring for clearer signals")
            insights.append("Use this period to gather additional information")
        
        # Quality-based insights
        if quality_metrics.overall_score < 0.6:
            insights.append("Seek additional data sources to improve analysis confidence")
        
        if len(conflicts) > 3:
            insights.append("High number of conflicting signals - consider waiting for market clarity")
        
        # Risk-based insights
        high_risk_count = sum(1 for rf in report.risk_factors if rf.severity in ['high', 'critical'])
        if high_risk_count > 2:
            insights.append("Multiple high-risk factors present - consider reduced position size")
        
        # Data-specific insights
        if report.data_quality_score < 0.7:
            insights.append("Data quality concerns - validate key metrics independently")
        
        return insights
    
    def _generate_next_steps(
        self,
        report: UnifiedReport,
        quality_metrics: QualityMetrics,
        validation_report: CrossValidationReport
    ) -> List[str]:
        """Generate recommended next steps"""
        next_steps = []
        
        # Always include monitoring
        next_steps.append("Monitor key metrics and market conditions for changes")
        
        # Quality improvement steps
        if quality_metrics.overall_score < 0.8:
            next_steps.append("Gather additional data to improve analysis quality")
        
        if validation_report and len(validation_report.issues) > 2:
            next_steps.append("Investigate and resolve data inconsistencies")
        
        # Recommendation-specific steps
        if report.final_recommendation.value in ['strong_buy', 'buy']:
            next_steps.append("Prepare entry strategy with appropriate risk management")
            next_steps.append("Set target prices and stop-loss levels")
        elif report.final_recommendation.value in ['strong_sell', 'sell']:
            next_steps.append("Plan exit strategy considering market liquidity")
            next_steps.append("Review tax implications of selling")
        
        # Risk management steps
        if report.overall_risk_level in ['Medium', 'High']:
            next_steps.append("Implement additional risk management measures")
        
        # Follow-up analysis
        next_steps.append("Schedule follow-up analysis based on market developments")
        
        return next_steps
    
    def _track_analysis_performance(self, result: MasterAnalysisResult):
        """Track analysis performance for continuous improvement"""
        performance_data = {
            'timestamp': result.timestamp,
            'symbol': result.unified_report.symbol,
            'processing_time': result.processing_time,
            'quality_score': result.quality_metrics.overall_score,
            'confidence_score': result.unified_report.confidence_score,
            'conflicts_count': len(result.conflicts),
            'resolutions_count': len(result.resolutions),
            'data_points_count': len(result.fused_data),
            'sources_count': len(result.unified_report.sources_used)
        }
        
        self.analysis_history.append(performance_data)
        
        # Keep only last 100 analyses
        if len(self.analysis_history) > 100:
            self.analysis_history = self.analysis_history[-100:]
        
        # Update performance metrics
        self._update_performance_metrics()
    
    def _update_performance_metrics(self):
        """Update aggregate performance metrics"""
        if not self.analysis_history:
            return
        
        recent_analyses = self.analysis_history[-20:]  # Last 20 analyses
        
        self.performance_metrics = {
            'avg_processing_time': sum(a['processing_time'] for a in recent_analyses) / len(recent_analyses),
            'avg_quality_score': sum(a['quality_score'] for a in recent_analyses) / len(recent_analyses),
            'avg_confidence_score': sum(a['confidence_score'] for a in recent_analyses) / len(recent_analyses),
            'avg_conflicts_per_analysis': sum(a['conflicts_count'] for a in recent_analyses) / len(recent_analyses),
            'avg_data_points_per_analysis': sum(a['data_points_count'] for a in recent_analyses) / len(recent_analyses),
            'total_analyses': len(self.analysis_history)
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of the Master Analyst"""
        return {
            'performance_metrics': self.performance_metrics,
            'recent_analyses_count': len(self.analysis_history),
            'system_status': self._assess_system_status()
        }
    
    def _assess_system_status(self) -> str:
        """Assess overall system status"""
        if not self.performance_metrics:
            return "Insufficient data"
        
        avg_quality = self.performance_metrics.get('avg_quality_score', 0)
        avg_confidence = self.performance_metrics.get('avg_confidence_score', 0)
        avg_processing_time = self.performance_metrics.get('avg_processing_time', 0)
        
        if avg_quality > 0.8 and avg_confidence > 0.7 and avg_processing_time < 10:
            return "Excellent"
        elif avg_quality > 0.7 and avg_confidence > 0.6 and avg_processing_time < 15:
            return "Good"
        elif avg_quality > 0.6 and avg_confidence > 0.5 and avg_processing_time < 20:
            return "Acceptable"
        else:
            return "Needs Improvement"
    
    def export_analysis_result(
        self, 
        result: MasterAnalysisResult, 
        format: str = "text"
    ) -> str:
        """Export analysis result in specified format"""
        if format.lower() == "json":
            return self.report_generator.format_report_as_json(result.unified_report)
        else:
            # Default to text format with additional Master Analyst insights
            text_report = self.report_generator.format_report_as_text(result.unified_report)
            
            # Add Master Analyst specific sections
            additional_sections = []
            
            additional_sections.append("\n## Master Analyst Insights")
            additional_sections.append(result.executive_summary)
            
            additional_sections.append("\n## Actionable Insights")
            for insight in result.actionable_insights:
                additional_sections.append(f"• {insight}")
            
            additional_sections.append("\n## Recommended Next Steps")
            for step in result.next_steps:
                additional_sections.append(f"• {step}")
            
            additional_sections.append("\n## Analysis Quality Breakdown")
            additional_sections.append(f"• Overall Quality Score: {result.quality_metrics.overall_score:.1%}")
            additional_sections.append(f"• Confidence Level: {result.quality_metrics.confidence_level.value}")
            additional_sections.append(f"• Processing Time: {result.processing_time:.2f} seconds")
            additional_sections.append(f"• Data Points Analyzed: {len(result.fused_data)}")
            additional_sections.append(f"• Conflicts Detected: {len(result.conflicts)}")
            additional_sections.append(f"• Conflicts Resolved: {len(result.resolutions)}")
            
            return text_report + "\n".join(additional_sections)
    
    def quick_analysis(
        self,
        symbol: str,
        agent_reports: Dict[str, str]
    ) -> str:
        """Perform quick analysis and return summary"""
        result = self.analyze(symbol, agent_reports)
        
        quick_summary = f"""
🎯 **{symbol} Analysis Summary**

**Recommendation:** {result.unified_report.final_recommendation.value.upper()}
**Confidence:** {result.unified_report.confidence_score:.0%}
**Quality Score:** {result.quality_metrics.overall_score:.0%}
**Risk Level:** {result.unified_report.overall_risk_level}

**Key Insight:** {result.actionable_insights[0] if result.actionable_insights else 'No specific insights available'}

**Next Step:** {result.next_steps[0] if result.next_steps else 'Monitor market conditions'}

*Analysis completed in {result.processing_time:.1f}s using {len(result.unified_report.sources_used)} sources*
        """
        
        return quick_summary.strip()
