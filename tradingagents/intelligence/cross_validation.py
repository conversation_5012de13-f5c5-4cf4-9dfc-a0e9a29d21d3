"""
Cross-validation Logic for TradingAgents Intelligence System
Validates data consistency and identifies conflicts between agents.
"""

from typing import Dict, List, Any, Optional, Tu<PERSON>, Set
from dataclasses import dataclass, field
from enum import Enum
import math
from datetime import datetime

from .data_fusion import DataPoint, FusedData, DataSource, DataCategory


class ValidationResult(Enum):
    """Results of cross-validation checks"""
    CONSISTENT = "consistent"
    MINOR_DISCREPANCY = "minor_discrepancy"
    MAJOR_CONFLICT = "major_conflict"
    INSUFFICIENT_DATA = "insufficient_data"


class ValidationSeverity(Enum):
    """Severity levels for validation issues"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationIssue:
    """Represents a validation issue found during cross-checking"""
    issue_type: str
    severity: ValidationSeverity
    description: str
    affected_sources: List[DataSource]
    conflicting_values: Dict[DataSource, Any]
    confidence_impact: float  # How much this affects overall confidence (0.0 to 1.0)
    suggested_resolution: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CrossValidationReport:
    """Comprehensive report of cross-validation results"""
    overall_result: ValidationResult
    consistency_score: float  # 0.0 to 1.0
    issues: List[ValidationIssue]
    validated_metrics: Dict[str, ValidationResult]
    recommendations: List[str]
    timestamp: datetime = field(default_factory=datetime.now)


class CrossValidator:
    """Main class for cross-validation logic"""
    
    def __init__(self):
        """Initialize cross-validator with validation rules and thresholds"""
        self.validation_rules = self._initialize_validation_rules()
        self.tolerance_thresholds = self._initialize_tolerance_thresholds()
        self.cross_check_mappings = self._initialize_cross_check_mappings()
    
    def _initialize_validation_rules(self) -> Dict[str, Dict[str, Any]]:
        """Initialize validation rules for different data types"""
        return {
            'earnings_consistency': {
                'sources': [DataSource.EARNINGS_SPECIALIST_AGENT, DataSource.FINANCIAL_REPORTS_ANALYST],
                'metrics': ['eps', 'revenue', 'earnings_growth'],
                'tolerance': 0.05,  # 5% tolerance
                'severity': ValidationSeverity.ERROR
            },
            'financial_ratios_consistency': {
                'sources': [DataSource.FUNDAMENTALS_ANALYST, DataSource.FINANCIAL_REPORTS_ANALYST],
                'metrics': ['pe_ratio', 'debt_ratio', 'roe', 'roa'],
                'tolerance': 0.10,  # 10% tolerance
                'severity': ValidationSeverity.WARNING
            },
            'volume_data_consistency': {
                'sources': [DataSource.MARKET_ANALYST, DataSource.VOLUME_ANALYSIS_AGENT],
                'metrics': ['volume', 'avg_volume', 'volume_trend'],
                'tolerance': 0.15,  # 15% tolerance
                'severity': ValidationSeverity.WARNING
            },
            'price_data_consistency': {
                'sources': [DataSource.MARKET_ANALYST, DataSource.VOLUME_ANALYSIS_AGENT],
                'metrics': ['current_price', 'close_price', 'price_change'],
                'tolerance': 0.02,  # 2% tolerance
                'severity': ValidationSeverity.ERROR
            },
            'sentiment_coherence': {
                'sources': [DataSource.NEWS_ANALYST, DataSource.SOCIAL_ANALYST],
                'metrics': ['sentiment_score', 'sentiment_trend'],
                'tolerance': 0.30,  # 30% tolerance (sentiment is more subjective)
                'severity': ValidationSeverity.INFO
            }
        }
    
    def _initialize_tolerance_thresholds(self) -> Dict[DataCategory, float]:
        """Initialize tolerance thresholds for different data categories"""
        return {
            DataCategory.PRICE_DATA: 0.02,        # 2% - Price should be very consistent
            DataCategory.VOLUME_DATA: 0.15,       # 15% - Volume can vary by source
            DataCategory.FINANCIAL_METRICS: 0.05,  # 5% - Financial data should be consistent
            DataCategory.EARNINGS_DATA: 0.03,      # 3% - Earnings should be very consistent
            DataCategory.SENTIMENT_DATA: 0.30,     # 30% - Sentiment is subjective
            DataCategory.NEWS_DATA: 0.50,          # 50% - News interpretation varies
            DataCategory.TECHNICAL_INDICATORS: 0.10, # 10% - Technical indicators can vary
            DataCategory.FUNDAMENTAL_RATIOS: 0.08   # 8% - Ratios should be fairly consistent
        }
    
    def _initialize_cross_check_mappings(self) -> Dict[str, List[Tuple[DataSource, str]]]:
        """Map metrics to sources that should provide them for cross-checking"""
        return {
            'eps': [
                (DataSource.EARNINGS_SPECIALIST_AGENT, 'eps'),
                (DataSource.FINANCIAL_REPORTS_ANALYST, 'earnings_per_share'),
                (DataSource.FUNDAMENTALS_ANALYST, 'eps')
            ],
            'revenue': [
                (DataSource.EARNINGS_SPECIALIST_AGENT, 'revenue'),
                (DataSource.FINANCIAL_REPORTS_ANALYST, 'total_revenue'),
                (DataSource.FUNDAMENTALS_ANALYST, 'revenue')
            ],
            'pe_ratio': [
                (DataSource.FUNDAMENTALS_ANALYST, 'pe_ratio'),
                (DataSource.FINANCIAL_REPORTS_ANALYST, 'price_earnings_ratio'),
                (DataSource.MARKET_ANALYST, 'pe_ratio')
            ],
            'volume': [
                (DataSource.MARKET_ANALYST, 'volume'),
                (DataSource.VOLUME_ANALYSIS_AGENT, 'trading_volume')
            ],
            'current_price': [
                (DataSource.MARKET_ANALYST, 'current_price'),
                (DataSource.VOLUME_ANALYSIS_AGENT, 'last_price')
            ],
            'sentiment': [
                (DataSource.NEWS_ANALYST, 'sentiment_score'),
                (DataSource.SOCIAL_ANALYST, 'sentiment_score')
            ]
        }
    
    def validate_data_consistency(self, data_points: List[DataPoint]) -> CrossValidationReport:
        """Perform comprehensive cross-validation of data points"""
        issues = []
        validated_metrics = {}
        
        # Group data points by metric for cross-checking
        metric_groups = self._group_by_metric(data_points)
        
        # Validate each metric group
        for metric_name, points in metric_groups.items():
            if len(points) > 1:  # Only validate if multiple sources
                validation_result, metric_issues = self._validate_metric_group(metric_name, points)
                validated_metrics[metric_name] = validation_result
                issues.extend(metric_issues)
        
        # Perform specialized validation checks
        specialized_issues = self._perform_specialized_validations(data_points)
        issues.extend(specialized_issues)
        
        # Calculate overall consistency score
        consistency_score = self._calculate_consistency_score(validated_metrics, issues)
        
        # Determine overall result
        overall_result = self._determine_overall_result(consistency_score, issues)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(issues, validated_metrics)
        
        return CrossValidationReport(
            overall_result=overall_result,
            consistency_score=consistency_score,
            issues=issues,
            validated_metrics=validated_metrics,
            recommendations=recommendations
        )
    
    def _group_by_metric(self, data_points: List[DataPoint]) -> Dict[str, List[DataPoint]]:
        """Group data points by metric name for cross-validation"""
        groups = {}
        for point in data_points:
            metric_key = self._normalize_metric_name(point.metric_name)
            if metric_key not in groups:
                groups[metric_key] = []
            groups[metric_key].append(point)
        return groups
    
    def _normalize_metric_name(self, metric_name: str) -> str:
        """Normalize metric names for consistent grouping"""
        # Map similar metric names to standard names
        name_mappings = {
            'earnings_per_share': 'eps',
            'price_earnings_ratio': 'pe_ratio',
            'total_revenue': 'revenue',
            'trading_volume': 'volume',
            'last_price': 'current_price',
            'sentiment_score': 'sentiment'
        }
        
        normalized = metric_name.lower().replace(' ', '_')
        return name_mappings.get(normalized, normalized)
    
    def _validate_metric_group(self, metric_name: str, points: List[DataPoint]) -> Tuple[ValidationResult, List[ValidationIssue]]:
        """Validate a group of data points for the same metric"""
        issues = []
        
        # Extract numeric values for comparison
        numeric_points = [(p, p.value) for p in points if isinstance(p.value, (int, float))]
        
        if len(numeric_points) < 2:
            return ValidationResult.INSUFFICIENT_DATA, issues
        
        # Calculate statistics
        values = [v for _, v in numeric_points]
        mean_value = sum(values) / len(values)
        max_value = max(values)
        min_value = min(values)
        
        # Determine tolerance for this metric
        tolerance = self._get_tolerance_for_metric(metric_name, points[0].category)
        
        # Check for outliers and conflicts
        if mean_value != 0:
            relative_range = (max_value - min_value) / abs(mean_value)
            
            if relative_range <= tolerance:
                result = ValidationResult.CONSISTENT
            elif relative_range <= tolerance * 2:
                result = ValidationResult.MINOR_DISCREPANCY
                issues.append(self._create_discrepancy_issue(metric_name, numeric_points, "minor"))
            else:
                result = ValidationResult.MAJOR_CONFLICT
                issues.append(self._create_discrepancy_issue(metric_name, numeric_points, "major"))
        else:
            # Handle zero mean case
            if max_value - min_value <= 0.01:  # Small absolute tolerance
                result = ValidationResult.CONSISTENT
            else:
                result = ValidationResult.MINOR_DISCREPANCY
                issues.append(self._create_discrepancy_issue(metric_name, numeric_points, "minor"))
        
        return result, issues
    
    def _get_tolerance_for_metric(self, metric_name: str, category: DataCategory) -> float:
        """Get tolerance threshold for a specific metric"""
        # Check if there's a specific rule for this metric
        for rule_name, rule in self.validation_rules.items():
            if metric_name in rule.get('metrics', []):
                return rule.get('tolerance', 0.10)
        
        # Fall back to category-based tolerance
        return self.tolerance_thresholds.get(category, 0.10)
    
    def _create_discrepancy_issue(self, metric_name: str, numeric_points: List[Tuple[DataPoint, Any]], severity_level: str) -> ValidationIssue:
        """Create a validation issue for data discrepancies"""
        points, values = zip(*numeric_points)
        
        severity_map = {
            "minor": ValidationSeverity.WARNING,
            "major": ValidationSeverity.ERROR
        }
        
        conflicting_values = {p.source: v for p, v in numeric_points}
        
        mean_value = sum(values) / len(values)
        max_deviation = max(abs(v - mean_value) / abs(mean_value) if mean_value != 0 else abs(v) for v in values)
        
        return ValidationIssue(
            issue_type=f"{metric_name}_discrepancy",
            severity=severity_map.get(severity_level, ValidationSeverity.WARNING),
            description=f"Discrepancy detected in {metric_name}: values vary by {max_deviation:.1%}",
            affected_sources=[p.source for p in points],
            conflicting_values=conflicting_values,
            confidence_impact=min(0.3, max_deviation),  # Cap impact at 30%
            suggested_resolution=f"Review {metric_name} calculation methods across sources",
            metadata={
                'mean_value': mean_value,
                'max_deviation': max_deviation,
                'value_range': (min(values), max(values))
            }
        )
    
    def _perform_specialized_validations(self, data_points: List[DataPoint]) -> List[ValidationIssue]:
        """Perform specialized validation checks"""
        issues = []
        
        # Check for logical inconsistencies
        issues.extend(self._check_logical_consistency(data_points))
        
        # Check for temporal consistency
        issues.extend(self._check_temporal_consistency(data_points))
        
        # Check for magnitude reasonableness
        issues.extend(self._check_magnitude_reasonableness(data_points))
        
        return issues
    
    def _check_logical_consistency(self, data_points: List[DataPoint]) -> List[ValidationIssue]:
        """Check for logical inconsistencies in the data"""
        issues = []
        
        # Example: Positive earnings but negative sentiment
        earnings_points = [p for p in data_points if 'eps' in p.metric_name.lower() and isinstance(p.value, (int, float))]
        sentiment_points = [p for p in data_points if 'sentiment' in p.metric_name.lower()]
        
        if earnings_points and sentiment_points:
            avg_earnings = sum(p.value for p in earnings_points) / len(earnings_points)
            avg_sentiment = sum(self._normalize_sentiment(p.value) for p in sentiment_points) / len(sentiment_points)
            
            # Check for logical inconsistency
            if avg_earnings > 0 and avg_sentiment < -0.5:
                issues.append(ValidationIssue(
                    issue_type="logical_inconsistency",
                    severity=ValidationSeverity.WARNING,
                    description="Positive earnings but strongly negative sentiment detected",
                    affected_sources=[p.source for p in earnings_points + sentiment_points],
                    conflicting_values={'avg_earnings': avg_earnings, 'avg_sentiment': avg_sentiment},
                    confidence_impact=0.15,
                    suggested_resolution="Investigate market conditions or sentiment drivers",
                    metadata={'earnings_value': avg_earnings, 'sentiment_value': avg_sentiment}
                ))
        
        return issues
    
    def _check_temporal_consistency(self, data_points: List[DataPoint]) -> List[ValidationIssue]:
        """Check for temporal consistency issues"""
        issues = []
        
        # Check if data points are from similar time periods
        timestamps = [p.timestamp for p in data_points if p.timestamp]
        if len(timestamps) > 1:
            time_range = max(timestamps) - min(timestamps)
            if time_range.total_seconds() > 3600:  # More than 1 hour difference
                issues.append(ValidationIssue(
                    issue_type="temporal_inconsistency",
                    severity=ValidationSeverity.INFO,
                    description=f"Data points span {time_range.total_seconds()/3600:.1f} hours",
                    affected_sources=[p.source for p in data_points],
                    conflicting_values={'time_range_hours': time_range.total_seconds()/3600},
                    confidence_impact=0.05,
                    suggested_resolution="Consider data freshness in analysis",
                    metadata={'min_timestamp': min(timestamps), 'max_timestamp': max(timestamps)}
                ))
        
        return issues
    
    def _check_magnitude_reasonableness(self, data_points: List[DataPoint]) -> List[ValidationIssue]:
        """Check if values are within reasonable ranges"""
        issues = []
        
        # Define reasonable ranges for different metrics
        reasonable_ranges = {
            'pe_ratio': (0, 100),
            'eps': (-10, 50),
            'volume': (0, float('inf')),
            'current_price': (0, float('inf')),
            'sentiment': (-1, 1)
        }
        
        for point in data_points:
            if isinstance(point.value, (int, float)):
                metric_key = self._normalize_metric_name(point.metric_name)
                if metric_key in reasonable_ranges:
                    min_val, max_val = reasonable_ranges[metric_key]
                    if not (min_val <= point.value <= max_val):
                        issues.append(ValidationIssue(
                            issue_type="unreasonable_magnitude",
                            severity=ValidationSeverity.WARNING,
                            description=f"{point.metric_name} value {point.value} outside reasonable range [{min_val}, {max_val}]",
                            affected_sources=[point.source],
                            conflicting_values={point.source: point.value},
                            confidence_impact=0.20,
                            suggested_resolution=f"Verify {point.metric_name} calculation or data source",
                            metadata={'expected_range': reasonable_ranges[metric_key], 'actual_value': point.value}
                        ))
        
        return issues
    
    def _normalize_sentiment(self, value: Any) -> float:
        """Normalize sentiment values to -1 to 1 scale"""
        if isinstance(value, (int, float)):
            return max(-1, min(1, value))
        elif isinstance(value, str):
            sentiment_map = {
                'positive': 0.5, 'bullish': 0.7,
                'negative': -0.5, 'bearish': -0.7,
                'neutral': 0.0
            }
            return sentiment_map.get(value.lower(), 0.0)
        return 0.0
    
    def _calculate_consistency_score(self, validated_metrics: Dict[str, ValidationResult], issues: List[ValidationIssue]) -> float:
        """Calculate overall consistency score (0.0 to 1.0)"""
        if not validated_metrics:
            return 0.5  # Neutral score if no validation performed
        
        # Base score from validation results
        result_scores = {
            ValidationResult.CONSISTENT: 1.0,
            ValidationResult.MINOR_DISCREPANCY: 0.7,
            ValidationResult.MAJOR_CONFLICT: 0.3,
            ValidationResult.INSUFFICIENT_DATA: 0.5
        }
        
        base_score = sum(result_scores[result] for result in validated_metrics.values()) / len(validated_metrics)
        
        # Adjust for issues
        total_impact = sum(issue.confidence_impact for issue in issues)
        adjusted_score = max(0.0, base_score - total_impact)
        
        return min(1.0, adjusted_score)
    
    def _determine_overall_result(self, consistency_score: float, issues: List[ValidationIssue]) -> ValidationResult:
        """Determine overall validation result"""
        critical_issues = [i for i in issues if i.severity == ValidationSeverity.CRITICAL]
        error_issues = [i for i in issues if i.severity == ValidationSeverity.ERROR]
        
        if critical_issues:
            return ValidationResult.MAJOR_CONFLICT
        elif error_issues or consistency_score < 0.5:
            return ValidationResult.MAJOR_CONFLICT
        elif consistency_score < 0.8:
            return ValidationResult.MINOR_DISCREPANCY
        else:
            return ValidationResult.CONSISTENT
    
    def _generate_recommendations(self, issues: List[ValidationIssue], validated_metrics: Dict[str, ValidationResult]) -> List[str]:
        """Generate actionable recommendations based on validation results"""
        recommendations = []
        
        # Group issues by type
        issue_types = {}
        for issue in issues:
            if issue.issue_type not in issue_types:
                issue_types[issue.issue_type] = []
            issue_types[issue.issue_type].append(issue)
        
        # Generate recommendations for each issue type
        for issue_type, type_issues in issue_types.items():
            if 'discrepancy' in issue_type:
                recommendations.append(f"Review data sources for {issue_type.replace('_discrepancy', '')} to resolve discrepancies")
            elif issue_type == 'logical_inconsistency':
                recommendations.append("Investigate market conditions that might explain sentiment-fundamental disconnects")
            elif issue_type == 'temporal_inconsistency':
                recommendations.append("Ensure data freshness and synchronization across sources")
            elif issue_type == 'unreasonable_magnitude':
                recommendations.append("Verify data quality and calculation methods for outlier values")
        
        # Add general recommendations based on consistency score
        consistent_count = sum(1 for result in validated_metrics.values() if result == ValidationResult.CONSISTENT)
        total_count = len(validated_metrics)
        
        if total_count > 0:
            consistency_ratio = consistent_count / total_count
            if consistency_ratio < 0.5:
                recommendations.append("Consider reviewing data collection and processing methods")
            elif consistency_ratio < 0.8:
                recommendations.append("Monitor data quality trends and implement additional validation checks")
        
        return recommendations
