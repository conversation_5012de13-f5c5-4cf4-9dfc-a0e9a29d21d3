"""
Unified Output Format for TradingAgents Intelligence System
Creates consolidated reports combining insights from all agents.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json

from .data_fusion import FusedData, DataSource, DataCategory
from .cross_validation import CrossValidationReport, ValidationResult
from .weighted_analysis import WeightedAnalysis, QualityScore
from .conflict_resolution import Conflict, Resolution


class ReportSection(Enum):
    """Sections of the unified report"""
    EXECUTIVE_SUMMARY = "executive_summary"
    MARKET_ANALYSIS = "market_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    TECHNICAL_ANALYSIS = "technical_analysis"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    DATA_QUALITY = "data_quality"
    RECOMMENDATIONS = "recommendations"


class RecommendationStrength(Enum):
    """Strength levels for recommendations"""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    WEAK_BUY = "weak_buy"
    HOLD = "hold"
    WEAK_SELL = "weak_sell"
    SELL = "sell"
    STRONG_SELL = "strong_sell"


@dataclass
class KeyMetric:
    """Represents a key metric in the report"""
    name: str
    value: Any
    unit: str
    confidence: float
    trend: str  # "up", "down", "stable"
    significance: str  # "high", "medium", "low"
    source_count: int
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RiskFactor:
    """Represents a risk factor"""
    category: str
    description: str
    severity: str  # "low", "medium", "high", "critical"
    probability: float  # 0.0 to 1.0
    impact: str
    mitigation: str
    sources: List[DataSource] = field(default_factory=list)


@dataclass
class UnifiedReport:
    """Comprehensive unified report structure"""
    # Header Information
    symbol: str
    analysis_date: datetime
    report_id: str
    
    # Executive Summary
    final_recommendation: RecommendationStrength
    confidence_score: float
    key_highlights: List[str]
    
    # Analysis Sections
    market_analysis: Dict[str, Any]
    fundamental_analysis: Dict[str, Any]
    technical_analysis: Dict[str, Any]
    sentiment_analysis: Dict[str, Any]
    
    # Key Metrics
    key_metrics: List[KeyMetric]
    
    # Risk Assessment
    risk_factors: List[RiskFactor]
    overall_risk_level: str
    
    # Data Quality
    data_quality_score: float
    source_reliability: Dict[DataSource, float]
    validation_summary: str
    conflicts_resolved: int
    
    # Supporting Information
    contributing_factors: List[str]
    methodology_notes: List[str]
    disclaimers: List[str]
    
    # Metadata
    processing_time: float
    sources_used: List[DataSource]
    metadata: Dict[str, Any] = field(default_factory=dict)


class UnifiedReportGenerator:
    """Generates unified reports from intelligence system outputs"""
    
    def __init__(self):
        """Initialize the report generator"""
        self.report_templates = self._initialize_report_templates()
        self.metric_formatters = self._initialize_metric_formatters()
        self.risk_categories = self._initialize_risk_categories()
    
    def _initialize_report_templates(self) -> Dict[ReportSection, str]:
        """Initialize templates for different report sections"""
        return {
            ReportSection.EXECUTIVE_SUMMARY: """
## Executive Summary

**Recommendation:** {recommendation} (Confidence: {confidence:.0%})

**Key Highlights:**
{highlights}

**Overall Assessment:** {assessment}
            """,
            
            ReportSection.MARKET_ANALYSIS: """
## Market Analysis

**Current Price:** {current_price}
**Price Trend:** {price_trend}
**Volume Analysis:** {volume_analysis}
**Technical Indicators:** {technical_indicators}

**Market Sentiment:** {market_sentiment}
            """,
            
            ReportSection.FUNDAMENTAL_ANALYSIS: """
## Fundamental Analysis

**Financial Health:** {financial_health}
**Key Ratios:** {key_ratios}
**Earnings Quality:** {earnings_quality}
**Growth Prospects:** {growth_prospects}

**Valuation:** {valuation}
            """,
            
            ReportSection.RISK_ASSESSMENT: """
## Risk Assessment

**Overall Risk Level:** {risk_level}

**Key Risk Factors:**
{risk_factors}

**Risk Mitigation:** {risk_mitigation}
            """
        }
    
    def _initialize_metric_formatters(self) -> Dict[str, callable]:
        """Initialize formatters for different metric types"""
        return {
            'currency': lambda x: f"${x:,.2f}" if isinstance(x, (int, float)) else str(x),
            'percentage': lambda x: f"{x:.1%}" if isinstance(x, (int, float)) else str(x),
            'ratio': lambda x: f"{x:.2f}" if isinstance(x, (int, float)) else str(x),
            'volume': lambda x: f"{x:,.0f}" if isinstance(x, (int, float)) else str(x),
            'score': lambda x: f"{x:.2f}" if isinstance(x, (int, float)) else str(x)
        }
    
    def _initialize_risk_categories(self) -> Dict[str, Dict[str, str]]:
        """Initialize risk categories and their descriptions"""
        return {
            'market_risk': {
                'name': 'Market Risk',
                'description': 'Risk from overall market movements'
            },
            'liquidity_risk': {
                'name': 'Liquidity Risk',
                'description': 'Risk from low trading volume or market depth'
            },
            'fundamental_risk': {
                'name': 'Fundamental Risk',
                'description': 'Risk from company-specific financial issues'
            },
            'sentiment_risk': {
                'name': 'Sentiment Risk',
                'description': 'Risk from negative market sentiment'
            },
            'data_quality_risk': {
                'name': 'Data Quality Risk',
                'description': 'Risk from inconsistent or unreliable data'
            }
        }
    
    def generate_unified_report(
        self,
        symbol: str,
        fused_data: List[FusedData],
        validation_report: CrossValidationReport,
        weighted_analysis: WeightedAnalysis,
        conflicts: List[Conflict],
        resolutions: Dict[str, Resolution],
        processing_time: float
    ) -> UnifiedReport:
        """Generate comprehensive unified report"""
        
        # Generate unique report ID
        report_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Extract key metrics
        key_metrics = self._extract_key_metrics(fused_data)
        
        # Generate analysis sections
        market_analysis = self._generate_market_analysis(fused_data, weighted_analysis)
        fundamental_analysis = self._generate_fundamental_analysis(fused_data, weighted_analysis)
        technical_analysis = self._generate_technical_analysis(fused_data, weighted_analysis)
        sentiment_analysis = self._generate_sentiment_analysis(fused_data, weighted_analysis)
        
        # Assess risks
        risk_factors = self._assess_risk_factors(
            fused_data, validation_report, conflicts, weighted_analysis
        )
        overall_risk_level = self._calculate_overall_risk_level(risk_factors)
        
        # Generate executive summary components
        final_recommendation = self._determine_final_recommendation(weighted_analysis)
        key_highlights = self._generate_key_highlights(
            fused_data, weighted_analysis, risk_factors
        )
        
        # Calculate data quality metrics
        data_quality_score = self._calculate_data_quality_score(
            validation_report, conflicts, len(resolutions)
        )
        source_reliability = self._extract_source_reliability(weighted_analysis)
        validation_summary = self._generate_validation_summary(validation_report)
        
        # Generate supporting information
        contributing_factors = weighted_analysis.contributing_factors
        methodology_notes = self._generate_methodology_notes(fused_data, weighted_analysis)
        disclaimers = self._generate_disclaimers()
        
        return UnifiedReport(
            symbol=symbol,
            analysis_date=datetime.now(),
            report_id=report_id,
            final_recommendation=final_recommendation,
            confidence_score=weighted_analysis.confidence_score,
            key_highlights=key_highlights,
            market_analysis=market_analysis,
            fundamental_analysis=fundamental_analysis,
            technical_analysis=technical_analysis,
            sentiment_analysis=sentiment_analysis,
            key_metrics=key_metrics,
            risk_factors=risk_factors,
            overall_risk_level=overall_risk_level,
            data_quality_score=data_quality_score,
            source_reliability=source_reliability,
            validation_summary=validation_summary,
            conflicts_resolved=len(resolutions),
            contributing_factors=contributing_factors,
            methodology_notes=methodology_notes,
            disclaimers=disclaimers,
            processing_time=processing_time,
            sources_used=list(weighted_analysis.weighted_signals.keys()),
            metadata={
                'total_data_points': len(fused_data),
                'validation_score': validation_report.consistency_score if validation_report else 0.0,
                'conflicts_detected': len(conflicts)
            }
        )
    
    def _extract_key_metrics(self, fused_data: List[FusedData]) -> List[KeyMetric]:
        """Extract key metrics from fused data"""
        key_metrics = []
        
        # Define important metrics to highlight
        important_metrics = {
            'current_price': {'unit': 'VND', 'type': 'currency'},
            'eps': {'unit': 'VND', 'type': 'currency'},
            'pe_ratio': {'unit': 'x', 'type': 'ratio'},
            'volume': {'unit': 'shares', 'type': 'volume'},
            'revenue': {'unit': 'VND', 'type': 'currency'},
            'sentiment': {'unit': 'score', 'type': 'score'}
        }
        
        for data in fused_data:
            metric_key = data.metric_name.lower()
            if metric_key in important_metrics:
                metric_info = important_metrics[metric_key]
                
                # Determine trend (simplified)
                trend = "stable"  # Would need historical data for proper trend analysis
                
                # Determine significance
                significance = "high" if data.confidence_score > 0.8 else "medium" if data.confidence_score > 0.5 else "low"
                
                key_metric = KeyMetric(
                    name=data.metric_name.title(),
                    value=data.primary_value,
                    unit=metric_info['unit'],
                    confidence=data.confidence_score,
                    trend=trend,
                    significance=significance,
                    source_count=len(data.contributing_sources),
                    metadata={
                        'consensus_level': data.consensus_level,
                        'category': data.category.value
                    }
                )
                key_metrics.append(key_metric)
        
        return key_metrics
    
    def _generate_market_analysis(
        self, 
        fused_data: List[FusedData], 
        weighted_analysis: WeightedAnalysis
    ) -> Dict[str, Any]:
        """Generate market analysis section"""
        market_data = {d for d in fused_data if d.category in [DataCategory.PRICE_DATA, DataCategory.VOLUME_DATA]}
        
        analysis = {
            'current_price': 'N/A',
            'price_trend': 'Neutral',
            'volume_analysis': 'Normal trading volume',
            'technical_indicators': 'Mixed signals',
            'market_sentiment': 'Neutral'
        }
        
        # Extract specific market metrics
        for data in market_data:
            if 'price' in data.metric_name.lower():
                analysis['current_price'] = self._format_metric(data.primary_value, 'currency')
            elif 'volume' in data.metric_name.lower():
                analysis['volume_analysis'] = f"Volume: {self._format_metric(data.primary_value, 'volume')}"
        
        # Determine overall market sentiment from weighted analysis
        market_signal = weighted_analysis.weighted_signals.get(DataSource.MARKET_ANALYST, 0.0)
        if market_signal > 0.3:
            analysis['market_sentiment'] = 'Bullish'
        elif market_signal < -0.3:
            analysis['market_sentiment'] = 'Bearish'
        
        return analysis
    
    def _generate_fundamental_analysis(
        self, 
        fused_data: List[FusedData], 
        weighted_analysis: WeightedAnalysis
    ) -> Dict[str, Any]:
        """Generate fundamental analysis section"""
        fundamental_data = {
            d for d in fused_data 
            if d.category in [DataCategory.FINANCIAL_METRICS, DataCategory.FUNDAMENTAL_RATIOS, DataCategory.EARNINGS_DATA]
        }
        
        analysis = {
            'financial_health': 'Stable',
            'key_ratios': 'Within normal ranges',
            'earnings_quality': 'Good',
            'growth_prospects': 'Moderate',
            'valuation': 'Fair value'
        }
        
        # Analyze fundamental signals
        fundamental_signals = [
            weighted_analysis.weighted_signals.get(DataSource.FUNDAMENTALS_ANALYST, 0.0),
            weighted_analysis.weighted_signals.get(DataSource.FINANCIAL_REPORTS_ANALYST, 0.0),
            weighted_analysis.weighted_signals.get(DataSource.EARNINGS_SPECIALIST_AGENT, 0.0)
        ]
        
        avg_fundamental_signal = sum(s for s in fundamental_signals if s != 0) / max(1, len([s for s in fundamental_signals if s != 0]))
        
        if avg_fundamental_signal > 0.4:
            analysis['financial_health'] = 'Strong'
            analysis['growth_prospects'] = 'Positive'
        elif avg_fundamental_signal < -0.4:
            analysis['financial_health'] = 'Weak'
            analysis['growth_prospects'] = 'Concerning'
        
        return analysis
    
    def _generate_technical_analysis(
        self, 
        fused_data: List[FusedData], 
        weighted_analysis: WeightedAnalysis
    ) -> Dict[str, Any]:
        """Generate technical analysis section"""
        technical_data = {
            d for d in fused_data 
            if d.category in [DataCategory.TECHNICAL_INDICATORS, DataCategory.VOLUME_DATA]
        }
        
        analysis = {
            'trend_direction': 'Sideways',
            'momentum': 'Neutral',
            'support_resistance': 'Key levels identified',
            'volume_confirmation': 'Average',
            'technical_outlook': 'Neutral'
        }
        
        # Analyze technical signals
        volume_signal = weighted_analysis.weighted_signals.get(DataSource.VOLUME_ANALYSIS_AGENT, 0.0)
        market_signal = weighted_analysis.weighted_signals.get(DataSource.MARKET_ANALYST, 0.0)
        
        combined_technical = (volume_signal + market_signal) / 2
        
        if combined_technical > 0.3:
            analysis['trend_direction'] = 'Upward'
            analysis['momentum'] = 'Positive'
            analysis['technical_outlook'] = 'Bullish'
        elif combined_technical < -0.3:
            analysis['trend_direction'] = 'Downward'
            analysis['momentum'] = 'Negative'
            analysis['technical_outlook'] = 'Bearish'
        
        return analysis
    
    def _generate_sentiment_analysis(
        self, 
        fused_data: List[FusedData], 
        weighted_analysis: WeightedAnalysis
    ) -> Dict[str, Any]:
        """Generate sentiment analysis section"""
        sentiment_data = {d for d in fused_data if d.category == DataCategory.SENTIMENT_DATA}
        
        analysis = {
            'overall_sentiment': 'Neutral',
            'news_sentiment': 'Neutral',
            'social_sentiment': 'Neutral',
            'sentiment_trend': 'Stable',
            'sentiment_strength': 'Moderate'
        }
        
        # Analyze sentiment signals
        news_signal = weighted_analysis.weighted_signals.get(DataSource.NEWS_ANALYST, 0.0)
        social_signal = weighted_analysis.weighted_signals.get(DataSource.SOCIAL_ANALYST, 0.0)
        
        if news_signal > 0.2:
            analysis['news_sentiment'] = 'Positive'
        elif news_signal < -0.2:
            analysis['news_sentiment'] = 'Negative'
        
        if social_signal > 0.2:
            analysis['social_sentiment'] = 'Positive'
        elif social_signal < -0.2:
            analysis['social_sentiment'] = 'Negative'
        
        # Overall sentiment
        avg_sentiment = (news_signal + social_signal) / 2
        if avg_sentiment > 0.3:
            analysis['overall_sentiment'] = 'Positive'
            analysis['sentiment_strength'] = 'Strong'
        elif avg_sentiment < -0.3:
            analysis['overall_sentiment'] = 'Negative'
            analysis['sentiment_strength'] = 'Strong'
        
        return analysis
    
    def _assess_risk_factors(
        self, 
        fused_data: List[FusedData], 
        validation_report: CrossValidationReport,
        conflicts: List[Conflict],
        weighted_analysis: WeightedAnalysis
    ) -> List[RiskFactor]:
        """Assess various risk factors"""
        risk_factors = []
        
        # Data quality risks
        if validation_report and validation_report.consistency_score < 0.7:
            risk_factors.append(RiskFactor(
                category='data_quality_risk',
                description='Inconsistent data across sources may affect analysis reliability',
                severity='medium',
                probability=1.0 - validation_report.consistency_score,
                impact='Reduced confidence in recommendations',
                mitigation='Cross-validate with additional sources',
                sources=[]
            ))
        
        # Conflict risks
        if len(conflicts) > 2:
            risk_factors.append(RiskFactor(
                category='market_risk',
                description=f'Multiple conflicting signals detected ({len(conflicts)} conflicts)',
                severity='medium',
                probability=min(1.0, len(conflicts) / 5),
                impact='Increased uncertainty in market direction',
                mitigation='Monitor for resolution of conflicting indicators',
                sources=[]
            ))
        
        # Low confidence risks
        if weighted_analysis.confidence_score < 0.6:
            risk_factors.append(RiskFactor(
                category='fundamental_risk',
                description='Low overall confidence in analysis',
                severity='high',
                probability=1.0 - weighted_analysis.confidence_score,
                impact='Higher probability of incorrect recommendations',
                mitigation='Gather additional data and wait for clearer signals',
                sources=[]
            ))
        
        return risk_factors
    
    def _calculate_overall_risk_level(self, risk_factors: List[RiskFactor]) -> str:
        """Calculate overall risk level"""
        if not risk_factors:
            return 'Low'
        
        severity_scores = {
            'low': 1,
            'medium': 2,
            'high': 3,
            'critical': 4
        }
        
        total_risk = sum(severity_scores.get(rf.severity, 2) * rf.probability for rf in risk_factors)
        avg_risk = total_risk / len(risk_factors)
        
        if avg_risk >= 3:
            return 'High'
        elif avg_risk >= 2:
            return 'Medium'
        else:
            return 'Low'
    
    def _determine_final_recommendation(self, weighted_analysis: WeightedAnalysis) -> RecommendationStrength:
        """Determine final recommendation strength"""
        recommendation = weighted_analysis.final_recommendation
        confidence = weighted_analysis.confidence_score
        
        # Map basic recommendation to strength based on confidence
        if recommendation == "BUY":
            if confidence > 0.8:
                return RecommendationStrength.STRONG_BUY
            elif confidence > 0.6:
                return RecommendationStrength.BUY
            else:
                return RecommendationStrength.WEAK_BUY
        elif recommendation == "SELL":
            if confidence > 0.8:
                return RecommendationStrength.STRONG_SELL
            elif confidence > 0.6:
                return RecommendationStrength.SELL
            else:
                return RecommendationStrength.WEAK_SELL
        else:
            return RecommendationStrength.HOLD
    
    def _generate_key_highlights(
        self, 
        fused_data: List[FusedData], 
        weighted_analysis: WeightedAnalysis,
        risk_factors: List[RiskFactor]
    ) -> List[str]:
        """Generate key highlights for executive summary"""
        highlights = []
        
        # Add recommendation highlight
        highlights.append(f"Recommendation: {weighted_analysis.final_recommendation} with {weighted_analysis.confidence_score:.0%} confidence")
        
        # Add key metric highlights
        high_confidence_data = [d for d in fused_data if d.confidence_score > 0.8]
        if high_confidence_data:
            highlights.append(f"High confidence data available for {len(high_confidence_data)} key metrics")
        
        # Add risk highlights
        high_risk_factors = [rf for rf in risk_factors if rf.severity in ['high', 'critical']]
        if high_risk_factors:
            highlights.append(f"⚠️ {len(high_risk_factors)} high-risk factors identified")
        
        # Add data quality highlight
        if len(weighted_analysis.weighted_signals) >= 5:
            highlights.append(f"Comprehensive analysis using {len(weighted_analysis.weighted_signals)} data sources")
        
        return highlights
    
    def _calculate_data_quality_score(
        self, 
        validation_report: CrossValidationReport, 
        conflicts: List[Conflict],
        resolutions_count: int
    ) -> float:
        """Calculate overall data quality score"""
        base_score = validation_report.consistency_score if validation_report else 0.5
        
        # Penalize for unresolved conflicts
        conflict_penalty = len(conflicts) * 0.1
        
        # Bonus for successful conflict resolution
        resolution_bonus = resolutions_count * 0.05
        
        final_score = max(0.0, min(1.0, base_score - conflict_penalty + resolution_bonus))
        return final_score
    
    def _extract_source_reliability(self, weighted_analysis: WeightedAnalysis) -> Dict[DataSource, float]:
        """Extract source reliability scores"""
        return {
            source: quality_score.overall_score 
            for source, quality_score in weighted_analysis.quality_scores.items()
        }
    
    def _generate_validation_summary(self, validation_report: CrossValidationReport) -> str:
        """Generate validation summary text"""
        if not validation_report:
            return "No validation performed"
        
        result_descriptions = {
            ValidationResult.CONSISTENT: "Data is highly consistent across sources",
            ValidationResult.MINOR_DISCREPANCY: "Minor discrepancies detected but resolved",
            ValidationResult.MAJOR_CONFLICT: "Significant conflicts detected and addressed",
            ValidationResult.INSUFFICIENT_DATA: "Limited data available for validation"
        }
        
        return result_descriptions.get(validation_report.overall_result, "Validation completed")
    
    def _generate_methodology_notes(
        self, 
        fused_data: List[FusedData], 
        weighted_analysis: WeightedAnalysis
    ) -> List[str]:
        """Generate methodology notes"""
        notes = [
            "Analysis combines data from multiple specialized agents",
            "Dynamic weighting based on source reliability and data quality",
            "Cross-validation performed to ensure data consistency",
            "Conflicts resolved using hybrid resolution strategies"
        ]
        
        if len(fused_data) > 10:
            notes.append(f"Comprehensive dataset with {len(fused_data)} data points analyzed")
        
        return notes
    
    def _generate_disclaimers(self) -> List[str]:
        """Generate standard disclaimers"""
        return [
            "This analysis is for informational purposes only and should not be considered as financial advice",
            "Past performance does not guarantee future results",
            "All investments carry risk of loss",
            "Please consult with a qualified financial advisor before making investment decisions",
            "Data sources and analysis methods may have limitations that could affect accuracy"
        ]
    
    def _format_metric(self, value: Any, metric_type: str) -> str:
        """Format metric value according to its type"""
        formatter = self.metric_formatters.get(metric_type, str)
        return formatter(value)
    
    def format_report_as_text(self, report: UnifiedReport) -> str:
        """Format unified report as readable text"""
        sections = []
        
        # Header
        sections.append(f"# Investment Analysis Report: {report.symbol}")
        sections.append(f"Generated: {report.analysis_date.strftime('%Y-%m-%d %H:%M:%S')}")
        sections.append(f"Report ID: {report.report_id}")
        sections.append("")
        
        # Executive Summary
        sections.append("## Executive Summary")
        sections.append(f"**Final Recommendation:** {report.final_recommendation.value.upper()}")
        sections.append(f"**Confidence Score:** {report.confidence_score:.0%}")
        sections.append("")
        sections.append("**Key Highlights:**")
        for highlight in report.key_highlights:
            sections.append(f"• {highlight}")
        sections.append("")
        
        # Key Metrics
        sections.append("## Key Metrics")
        for metric in report.key_metrics:
            sections.append(f"• **{metric.name}:** {metric.value} {metric.unit} (Confidence: {metric.confidence:.0%})")
        sections.append("")
        
        # Analysis Sections
        sections.append("## Market Analysis")
        for key, value in report.market_analysis.items():
            sections.append(f"• **{key.replace('_', ' ').title()}:** {value}")
        sections.append("")
        
        sections.append("## Fundamental Analysis")
        for key, value in report.fundamental_analysis.items():
            sections.append(f"• **{key.replace('_', ' ').title()}:** {value}")
        sections.append("")
        
        # Risk Assessment
        sections.append("## Risk Assessment")
        sections.append(f"**Overall Risk Level:** {report.overall_risk_level}")
        sections.append("")
        if report.risk_factors:
            sections.append("**Key Risk Factors:**")
            for risk in report.risk_factors:
                sections.append(f"• **{risk.category.replace('_', ' ').title()}:** {risk.description} (Severity: {risk.severity})")
        sections.append("")
        
        # Data Quality
        sections.append("## Data Quality Assessment")
        sections.append(f"• **Overall Data Quality:** {report.data_quality_score:.0%}")
        sections.append(f"• **Validation Summary:** {report.validation_summary}")
        sections.append(f"• **Conflicts Resolved:** {report.conflicts_resolved}")
        sections.append(f"• **Sources Used:** {len(report.sources_used)}")
        sections.append("")
        
        # Disclaimers
        sections.append("## Important Disclaimers")
        for disclaimer in report.disclaimers:
            sections.append(f"• {disclaimer}")
        
        return "\n".join(sections)
    
    def format_report_as_json(self, report: UnifiedReport) -> str:
        """Format unified report as JSON"""
        # Convert dataclass to dictionary for JSON serialization
        report_dict = {
            'symbol': report.symbol,
            'analysis_date': report.analysis_date.isoformat(),
            'report_id': report.report_id,
            'final_recommendation': report.final_recommendation.value,
            'confidence_score': report.confidence_score,
            'key_highlights': report.key_highlights,
            'market_analysis': report.market_analysis,
            'fundamental_analysis': report.fundamental_analysis,
            'technical_analysis': report.technical_analysis,
            'sentiment_analysis': report.sentiment_analysis,
            'key_metrics': [
                {
                    'name': m.name,
                    'value': m.value,
                    'unit': m.unit,
                    'confidence': m.confidence,
                    'trend': m.trend,
                    'significance': m.significance,
                    'source_count': m.source_count
                } for m in report.key_metrics
            ],
            'risk_factors': [
                {
                    'category': rf.category,
                    'description': rf.description,
                    'severity': rf.severity,
                    'probability': rf.probability,
                    'impact': rf.impact,
                    'mitigation': rf.mitigation
                } for rf in report.risk_factors
            ],
            'overall_risk_level': report.overall_risk_level,
            'data_quality_score': report.data_quality_score,
            'source_reliability': {k.value: v for k, v in report.source_reliability.items()},
            'validation_summary': report.validation_summary,
            'conflicts_resolved': report.conflicts_resolved,
            'contributing_factors': report.contributing_factors,
            'methodology_notes': report.methodology_notes,
            'disclaimers': report.disclaimers,
            'processing_time': report.processing_time,
            'sources_used': [s.value for s in report.sources_used],
            'metadata': report.metadata
        }
        
        return json.dumps(report_dict, indent=2, ensure_ascii=False)
