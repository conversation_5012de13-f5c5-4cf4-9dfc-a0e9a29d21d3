"""
Data Fusion Strategy for TradingAgents Intelligence System
Combines insights from multiple agents while avoiding redundancy and conflicts.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import re
from datetime import datetime


class DataSource(Enum):
    """Enum for different data sources and their reliability levels"""
    MARKET_ANALYST = "market_analyst"
    FUNDAMENTALS_ANALYST = "fundamentals_analyst"
    NEWS_ANALYST = "news_analyst"
    SOCIAL_ANALYST = "social_analyst"
    FINANCIAL_REPORTS_ANALYST = "financial_reports_analyst"
    VOLUME_ANALYSIS_AGENT = "volume_analysis_agent"
    EARNINGS_SPECIALIST_AGENT = "earnings_specialist_agent"


class DataCategory(Enum):
    """Categories of financial data for fusion"""
    PRICE_DATA = "price_data"
    VOLUME_DATA = "volume_data"
    FINANCIAL_METRICS = "financial_metrics"
    EARNINGS_DATA = "earnings_data"
    SENTIMENT_DATA = "sentiment_data"
    NEWS_DATA = "news_data"
    TECHNICAL_INDICATORS = "technical_indicators"
    FUNDAMENTAL_RATIOS = "fundamental_ratios"


@dataclass
class DataPoint:
    """Represents a single data point from an agent"""
    source: DataSource
    category: DataCategory
    metric_name: str
    value: Any
    confidence: float  # 0.0 to 1.0
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError("Confidence must be between 0.0 and 1.0")


@dataclass
class FusedData:
    """Represents fused data from multiple sources"""
    category: DataCategory
    metric_name: str
    primary_value: Any
    confidence_score: float
    contributing_sources: List[DataSource]
    source_values: Dict[DataSource, Any]
    consensus_level: float  # How much sources agree (0.0 to 1.0)
    metadata: Dict[str, Any] = field(default_factory=dict)


class DataFusionStrategy:
    """Main class for data fusion strategy implementation"""
    
    def __init__(self):
        """Initialize the data fusion strategy with source reliability weights"""
        self.source_weights = self._initialize_source_weights()
        self.category_mappings = self._initialize_category_mappings()
        self.fusion_rules = self._initialize_fusion_rules()
    
    def _initialize_source_weights(self) -> Dict[DataSource, float]:
        """Initialize reliability weights for each data source"""
        return {
            # Custom API-powered agents have higher weights
            DataSource.FINANCIAL_REPORTS_ANALYST: 0.95,  # Highest - structured financial data
            DataSource.EARNINGS_SPECIALIST_AGENT: 0.90,   # Very high - specialized earnings data
            DataSource.VOLUME_ANALYSIS_AGENT: 0.85,       # High - specialized volume data
            
            # Enhanced standard agents
            DataSource.MARKET_ANALYST: 0.80,              # High - market data (enhanced with API)
            DataSource.FUNDAMENTALS_ANALYST: 0.75,        # Good - fundamental data (enhanced with API)
            
            # Standard agents
            DataSource.NEWS_ANALYST: 0.70,                # Moderate - news sentiment
            DataSource.SOCIAL_ANALYST: 0.65,              # Lower - social sentiment (more volatile)
        }
    
    def _initialize_category_mappings(self) -> Dict[DataSource, List[DataCategory]]:
        """Map each agent to the data categories they provide"""
        return {
            DataSource.MARKET_ANALYST: [
                DataCategory.PRICE_DATA,
                DataCategory.VOLUME_DATA,
                DataCategory.TECHNICAL_INDICATORS
            ],
            DataSource.FUNDAMENTALS_ANALYST: [
                DataCategory.FINANCIAL_METRICS,
                DataCategory.FUNDAMENTAL_RATIOS
            ],
            DataSource.NEWS_ANALYST: [
                DataCategory.NEWS_DATA,
                DataCategory.SENTIMENT_DATA
            ],
            DataSource.SOCIAL_ANALYST: [
                DataCategory.SENTIMENT_DATA
            ],
            DataSource.FINANCIAL_REPORTS_ANALYST: [
                DataCategory.FINANCIAL_METRICS,
                DataCategory.FUNDAMENTAL_RATIOS,
                DataCategory.EARNINGS_DATA
            ],
            DataSource.VOLUME_ANALYSIS_AGENT: [
                DataCategory.VOLUME_DATA,
                DataCategory.TECHNICAL_INDICATORS
            ],
            DataSource.EARNINGS_SPECIALIST_AGENT: [
                DataCategory.EARNINGS_DATA,
                DataCategory.FINANCIAL_METRICS
            ]
        }
    
    def _initialize_fusion_rules(self) -> Dict[DataCategory, str]:
        """Define fusion rules for each data category"""
        return {
            DataCategory.PRICE_DATA: "weighted_average",
            DataCategory.VOLUME_DATA: "highest_confidence",
            DataCategory.FINANCIAL_METRICS: "consensus_with_validation",
            DataCategory.EARNINGS_DATA: "specialist_priority",
            DataCategory.SENTIMENT_DATA: "weighted_sentiment_fusion",
            DataCategory.NEWS_DATA: "latest_with_relevance",
            DataCategory.TECHNICAL_INDICATORS: "weighted_average",
            DataCategory.FUNDAMENTAL_RATIOS: "cross_validated_consensus"
        }
    
    def extract_data_points(self, agent_reports: Dict[str, str]) -> List[DataPoint]:
        """Extract structured data points from agent reports"""
        data_points = []
        
        for agent_name, report in agent_reports.items():
            try:
                source = DataSource(agent_name.lower().replace(" ", "_").replace("analyst", "analyst"))
                extracted_points = self._parse_agent_report(source, report)
                data_points.extend(extracted_points)
            except ValueError:
                # Handle unknown agent names
                continue
        
        return data_points
    
    def _parse_agent_report(self, source: DataSource, report: str) -> List[DataPoint]:
        """Parse individual agent report to extract data points"""
        data_points = []
        
        # Define parsing patterns for different types of data
        patterns = {
            'price': r'(?:price|close|current).*?(\d+\.?\d*)',
            'volume': r'volume.*?(\d+(?:,\d{3})*)',
            'pe_ratio': r'p/e.*?(\d+\.?\d*)',
            'eps': r'eps.*?(\d+\.?\d*)',
            'revenue': r'revenue.*?(\d+(?:,\d{3})*(?:\.\d+)?)',
            'sentiment': r'sentiment.*?(positive|negative|neutral|bullish|bearish)',
        }
        
        confidence = self.source_weights.get(source, 0.5)
        timestamp = datetime.now()
        
        for metric, pattern in patterns.items():
            matches = re.findall(pattern, report.lower())
            if matches:
                # Take the first match and determine category
                value = matches[0]
                category = self._determine_category(metric)
                
                if category:
                    data_point = DataPoint(
                        source=source,
                        category=category,
                        metric_name=metric,
                        value=self._normalize_value(value),
                        confidence=confidence,
                        timestamp=timestamp,
                        metadata={'raw_text': report[:200]}  # Store snippet for reference
                    )
                    data_points.append(data_point)
        
        return data_points
    
    def _determine_category(self, metric: str) -> Optional[DataCategory]:
        """Determine data category based on metric name"""
        category_map = {
            'price': DataCategory.PRICE_DATA,
            'volume': DataCategory.VOLUME_DATA,
            'pe_ratio': DataCategory.FUNDAMENTAL_RATIOS,
            'eps': DataCategory.EARNINGS_DATA,
            'revenue': DataCategory.FINANCIAL_METRICS,
            'sentiment': DataCategory.SENTIMENT_DATA,
        }
        return category_map.get(metric)
    
    def _normalize_value(self, value: str) -> Any:
        """Normalize extracted values to appropriate types"""
        # Remove commas and convert to appropriate type
        if isinstance(value, str):
            if value.lower() in ['positive', 'bullish']:
                return 1.0
            elif value.lower() in ['negative', 'bearish']:
                return -1.0
            elif value.lower() == 'neutral':
                return 0.0
            else:
                # Try to convert to number
                try:
                    clean_value = value.replace(',', '')
                    if '.' in clean_value:
                        return float(clean_value)
                    else:
                        return int(clean_value)
                except ValueError:
                    return value
        return value
    
    def fuse_data_points(self, data_points: List[DataPoint]) -> List[FusedData]:
        """Fuse data points from multiple sources using appropriate strategies"""
        # Group data points by category and metric
        grouped_data = {}
        for point in data_points:
            key = (point.category, point.metric_name)
            if key not in grouped_data:
                grouped_data[key] = []
            grouped_data[key].append(point)
        
        fused_results = []
        for (category, metric_name), points in grouped_data.items():
            if len(points) > 1:  # Only fuse if multiple sources
                fused = self._apply_fusion_rule(category, metric_name, points)
                fused_results.append(fused)
            else:
                # Single source - convert to fused format
                point = points[0]
                fused = FusedData(
                    category=category,
                    metric_name=metric_name,
                    primary_value=point.value,
                    confidence_score=point.confidence,
                    contributing_sources=[point.source],
                    source_values={point.source: point.value},
                    consensus_level=1.0,  # Single source = perfect consensus
                    metadata=point.metadata
                )
                fused_results.append(fused)
        
        return fused_results
    
    def _apply_fusion_rule(self, category: DataCategory, metric_name: str, points: List[DataPoint]) -> FusedData:
        """Apply appropriate fusion rule based on data category"""
        rule = self.fusion_rules.get(category, "weighted_average")
        
        if rule == "weighted_average":
            return self._weighted_average_fusion(category, metric_name, points)
        elif rule == "highest_confidence":
            return self._highest_confidence_fusion(category, metric_name, points)
        elif rule == "consensus_with_validation":
            return self._consensus_validation_fusion(category, metric_name, points)
        elif rule == "specialist_priority":
            return self._specialist_priority_fusion(category, metric_name, points)
        elif rule == "weighted_sentiment_fusion":
            return self._weighted_sentiment_fusion(category, metric_name, points)
        else:
            # Default to weighted average
            return self._weighted_average_fusion(category, metric_name, points)
    
    def _weighted_average_fusion(self, category: DataCategory, metric_name: str, points: List[DataPoint]) -> FusedData:
        """Fuse numeric data using weighted average"""
        numeric_points = [p for p in points if isinstance(p.value, (int, float))]
        
        if not numeric_points:
            # Fallback to highest confidence if no numeric data
            return self._highest_confidence_fusion(category, metric_name, points)
        
        total_weight = sum(p.confidence * self.source_weights[p.source] for p in numeric_points)
        weighted_sum = sum(p.value * p.confidence * self.source_weights[p.source] for p in numeric_points)
        
        if total_weight > 0:
            primary_value = weighted_sum / total_weight
        else:
            primary_value = numeric_points[0].value
        
        # Calculate consensus level
        values = [p.value for p in numeric_points]
        consensus = self._calculate_consensus(values)
        
        # Calculate overall confidence
        confidence_score = min(1.0, total_weight / len(numeric_points))
        
        return FusedData(
            category=category,
            metric_name=metric_name,
            primary_value=primary_value,
            confidence_score=confidence_score,
            contributing_sources=[p.source for p in points],
            source_values={p.source: p.value for p in points},
            consensus_level=consensus,
            metadata={'fusion_method': 'weighted_average', 'num_sources': len(points)}
        )
    
    def _highest_confidence_fusion(self, category: DataCategory, metric_name: str, points: List[DataPoint]) -> FusedData:
        """Select value from highest confidence source"""
        best_point = max(points, key=lambda p: p.confidence * self.source_weights[p.source])
        
        return FusedData(
            category=category,
            metric_name=metric_name,
            primary_value=best_point.value,
            confidence_score=best_point.confidence,
            contributing_sources=[p.source for p in points],
            source_values={p.source: p.value for p in points},
            consensus_level=self._calculate_consensus([p.value for p in points]),
            metadata={'fusion_method': 'highest_confidence', 'selected_source': best_point.source.value}
        )
    
    def _calculate_consensus(self, values: List[Any]) -> float:
        """Calculate consensus level among values (0.0 to 1.0)"""
        if len(values) <= 1:
            return 1.0
        
        # For numeric values, calculate coefficient of variation
        numeric_values = [v for v in values if isinstance(v, (int, float))]
        if len(numeric_values) >= 2:
            mean_val = sum(numeric_values) / len(numeric_values)
            if mean_val != 0:
                variance = sum((v - mean_val) ** 2 for v in numeric_values) / len(numeric_values)
                cv = (variance ** 0.5) / abs(mean_val)
                # Convert CV to consensus (lower CV = higher consensus)
                return max(0.0, 1.0 - min(1.0, cv))
        
        # For non-numeric values, check exact matches
        unique_values = set(str(v) for v in values)
        return 1.0 / len(unique_values)  # More unique values = lower consensus
