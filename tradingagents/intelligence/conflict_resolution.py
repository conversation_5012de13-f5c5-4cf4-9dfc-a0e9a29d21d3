"""
Conflict Resolution Engine for TradingAgents Intelligence System
Resolves conflicting signals and recommendations from multiple agents.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import math
from datetime import datetime

from .data_fusion import DataPoint, FusedData, DataSource, DataCategory
from .cross_validation import CrossValidationReport, ValidationResult, ValidationSeverity
from .weighted_analysis import WeightedAnalysis, QualityScore


class ConflictType(Enum):
    """Types of conflicts that can occur"""
    SIGNAL_CONTRADICTION = "signal_contradiction"  # Buy vs Sell signals
    MAGNITUDE_DISCREPANCY = "magnitude_discrepancy"  # Different values for same metric
    TEMPORAL_INCONSISTENCY = "temporal_inconsistency"  # Time-based conflicts
    LOGICAL_INCONSISTENCY = "logical_inconsistency"  # Logically incompatible data
    CONFIDENCE_CONFLICT = "confidence_conflict"  # High confidence in conflicting data


class ResolutionStrategy(Enum):
    """Strategies for resolving conflicts"""
    HIGHEST_WEIGHT = "highest_weight"  # Use highest weighted source
    CONSENSUS_BUILDING = "consensus_building"  # Find middle ground
    SPECIALIST_PRIORITY = "specialist_priority"  # Defer to domain specialist
    TEMPORAL_PRIORITY = "temporal_priority"  # Use most recent data
    CONFIDENCE_PRIORITY = "confidence_priority"  # Use highest confidence source
    HYBRID_APPROACH = "hybrid_approach"  # Combine multiple strategies


@dataclass
class Conflict:
    """Represents a detected conflict between sources"""
    conflict_type: ConflictType
    severity: float  # 0.0 to 1.0
    involved_sources: List[DataSource]
    conflicting_data: Dict[DataSource, Any]
    metric_name: str
    category: DataCategory
    description: str
    suggested_strategy: ResolutionStrategy
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Resolution:
    """Result of conflict resolution"""
    resolved_value: Any
    confidence_score: float
    strategy_used: ResolutionStrategy
    contributing_sources: List[DataSource]
    excluded_sources: List[DataSource]
    reasoning: str
    metadata: Dict[str, Any] = field(default_factory=dict)


class ConflictResolutionEngine:
    """Main engine for detecting and resolving conflicts"""
    
    def __init__(self):
        """Initialize the conflict resolution engine"""
        self.resolution_strategies = self._initialize_resolution_strategies()
        self.conflict_thresholds = self._initialize_conflict_thresholds()
        self.specialist_domains = self._initialize_specialist_domains()
    
    def _initialize_resolution_strategies(self) -> Dict[ConflictType, ResolutionStrategy]:
        """Initialize default resolution strategies for each conflict type"""
        return {
            ConflictType.SIGNAL_CONTRADICTION: ResolutionStrategy.HYBRID_APPROACH,
            ConflictType.MAGNITUDE_DISCREPANCY: ResolutionStrategy.CONSENSUS_BUILDING,
            ConflictType.TEMPORAL_INCONSISTENCY: ResolutionStrategy.TEMPORAL_PRIORITY,
            ConflictType.LOGICAL_INCONSISTENCY: ResolutionStrategy.SPECIALIST_PRIORITY,
            ConflictType.CONFIDENCE_CONFLICT: ResolutionStrategy.CONFIDENCE_PRIORITY
        }
    
    def _initialize_conflict_thresholds(self) -> Dict[ConflictType, float]:
        """Initialize thresholds for detecting different types of conflicts"""
        return {
            ConflictType.SIGNAL_CONTRADICTION: 0.5,  # Opposite signals (buy vs sell)
            ConflictType.MAGNITUDE_DISCREPANCY: 0.2,  # 20% difference in values
            ConflictType.TEMPORAL_INCONSISTENCY: 3600,  # 1 hour time difference
            ConflictType.LOGICAL_INCONSISTENCY: 0.3,  # Logical incompatibility threshold
            ConflictType.CONFIDENCE_CONFLICT: 0.4  # High confidence in conflicting data
        }
    
    def _initialize_specialist_domains(self) -> Dict[DataCategory, List[DataSource]]:
        """Map data categories to their specialist sources"""
        return {
            DataCategory.EARNINGS_DATA: [
                DataSource.EARNINGS_SPECIALIST_AGENT,
                DataSource.FINANCIAL_REPORTS_ANALYST
            ],
            DataCategory.FINANCIAL_METRICS: [
                DataSource.FINANCIAL_REPORTS_ANALYST,
                DataSource.FUNDAMENTALS_ANALYST
            ],
            DataCategory.VOLUME_DATA: [
                DataSource.VOLUME_ANALYSIS_AGENT,
                DataSource.MARKET_ANALYST
            ],
            DataCategory.PRICE_DATA: [
                DataSource.MARKET_ANALYST,
                DataSource.VOLUME_ANALYSIS_AGENT
            ],
            DataCategory.SENTIMENT_DATA: [
                DataSource.NEWS_ANALYST,
                DataSource.SOCIAL_ANALYST
            ],
            DataCategory.FUNDAMENTAL_RATIOS: [
                DataSource.FINANCIAL_REPORTS_ANALYST,
                DataSource.FUNDAMENTALS_ANALYST
            ]
        }
    
    def detect_conflicts(
        self, 
        fused_data: List[FusedData], 
        validation_report: CrossValidationReport
    ) -> List[Conflict]:
        """Detect conflicts in the fused data"""
        conflicts = []
        
        # Check each fused data point for conflicts
        for data in fused_data:
            data_conflicts = self._analyze_fused_data_conflicts(data)
            conflicts.extend(data_conflicts)
        
        # Check for cross-metric logical conflicts
        logical_conflicts = self._detect_logical_conflicts(fused_data)
        conflicts.extend(logical_conflicts)
        
        # Check for validation-based conflicts
        if validation_report:
            validation_conflicts = self._extract_validation_conflicts(validation_report)
            conflicts.extend(validation_conflicts)
        
        # Sort conflicts by severity
        conflicts.sort(key=lambda c: c.severity, reverse=True)
        
        return conflicts
    
    def _analyze_fused_data_conflicts(self, data: FusedData) -> List[Conflict]:
        """Analyze a single fused data point for conflicts"""
        conflicts = []
        
        if len(data.source_values) < 2:
            return conflicts  # No conflict possible with single source
        
        # Check for magnitude discrepancies
        numeric_values = {
            source: value for source, value in data.source_values.items()
            if isinstance(value, (int, float))
        }
        
        if len(numeric_values) >= 2:
            magnitude_conflict = self._check_magnitude_discrepancy(data, numeric_values)
            if magnitude_conflict:
                conflicts.append(magnitude_conflict)
        
        # Check for signal contradictions
        signal_conflict = self._check_signal_contradiction(data)
        if signal_conflict:
            conflicts.append(signal_conflict)
        
        # Check for confidence conflicts
        confidence_conflict = self._check_confidence_conflict(data)
        if confidence_conflict:
            conflicts.append(confidence_conflict)
        
        return conflicts
    
    def _check_magnitude_discrepancy(
        self, 
        data: FusedData, 
        numeric_values: Dict[DataSource, float]
    ) -> Optional[Conflict]:
        """Check for significant magnitude discrepancies"""
        values = list(numeric_values.values())
        if len(values) < 2:
            return None
        
        max_val = max(values)
        min_val = min(values)
        mean_val = sum(values) / len(values)
        
        if mean_val != 0:
            relative_range = (max_val - min_val) / abs(mean_val)
        else:
            relative_range = max_val - min_val
        
        threshold = self.conflict_thresholds[ConflictType.MAGNITUDE_DISCREPANCY]
        
        if relative_range > threshold:
            severity = min(1.0, relative_range / (threshold * 2))
            
            return Conflict(
                conflict_type=ConflictType.MAGNITUDE_DISCREPANCY,
                severity=severity,
                involved_sources=list(numeric_values.keys()),
                conflicting_data=numeric_values,
                metric_name=data.metric_name,
                category=data.category,
                description=f"Magnitude discrepancy in {data.metric_name}: {relative_range:.1%} variation",
                suggested_strategy=ResolutionStrategy.CONSENSUS_BUILDING,
                metadata={
                    'relative_range': relative_range,
                    'threshold': threshold,
                    'values_range': (min_val, max_val),
                    'mean_value': mean_val
                }
            )
        
        return None
    
    def _check_signal_contradiction(self, data: FusedData) -> Optional[Conflict]:
        """Check for contradictory trading signals"""
        # Convert values to signals (-1 to 1)
        signals = {}
        for source, value in data.source_values.items():
            signal = self._convert_to_signal(value, data.metric_name)
            if signal is not None:
                signals[source] = signal
        
        if len(signals) < 2:
            return None
        
        signal_values = list(signals.values())
        max_signal = max(signal_values)
        min_signal = min(signal_values)
        
        # Check for contradictory signals (positive vs negative)
        if max_signal > 0.3 and min_signal < -0.3:
            severity = min(1.0, (max_signal - min_signal) / 2)
            
            return Conflict(
                conflict_type=ConflictType.SIGNAL_CONTRADICTION,
                severity=severity,
                involved_sources=list(signals.keys()),
                conflicting_data=signals,
                metric_name=data.metric_name,
                category=data.category,
                description=f"Signal contradiction in {data.metric_name}: bullish vs bearish signals",
                suggested_strategy=ResolutionStrategy.HYBRID_APPROACH,
                metadata={
                    'signal_range': (min_signal, max_signal),
                    'signal_spread': max_signal - min_signal
                }
            )
        
        return None
    
    def _check_confidence_conflict(self, data: FusedData) -> Optional[Conflict]:
        """Check for conflicts where high-confidence sources disagree"""
        if data.consensus_level > 0.7:
            return None  # High consensus, no conflict
        
        # This would require access to individual confidence scores
        # For now, use consensus level as proxy
        if data.confidence_score > 0.8 and data.consensus_level < 0.5:
            return Conflict(
                conflict_type=ConflictType.CONFIDENCE_CONFLICT,
                severity=0.6,
                involved_sources=data.contributing_sources,
                conflicting_data=data.source_values,
                metric_name=data.metric_name,
                category=data.category,
                description=f"High confidence but low consensus in {data.metric_name}",
                suggested_strategy=ResolutionStrategy.CONFIDENCE_PRIORITY,
                metadata={
                    'confidence_score': data.confidence_score,
                    'consensus_level': data.consensus_level
                }
            )
        
        return None
    
    def _detect_logical_conflicts(self, fused_data: List[FusedData]) -> List[Conflict]:
        """Detect logical inconsistencies across different metrics"""
        conflicts = []
        
        # Create metric lookup
        metrics = {data.metric_name: data for data in fused_data}
        
        # Check for logical inconsistencies
        # Example: Positive earnings but negative sentiment
        if 'eps' in metrics and 'sentiment' in metrics:
            eps_data = metrics['eps']
            sentiment_data = metrics['sentiment']
            
            # Get primary values
            eps_value = eps_data.primary_value if isinstance(eps_data.primary_value, (int, float)) else 0
            sentiment_value = self._convert_to_signal(sentiment_data.primary_value, 'sentiment')
            
            if eps_value > 0 and sentiment_value is not None and sentiment_value < -0.5:
                conflicts.append(Conflict(
                    conflict_type=ConflictType.LOGICAL_INCONSISTENCY,
                    severity=0.7,
                    involved_sources=eps_data.contributing_sources + sentiment_data.contributing_sources,
                    conflicting_data={'eps': eps_value, 'sentiment': sentiment_value},
                    metric_name='eps_sentiment_inconsistency',
                    category=DataCategory.FINANCIAL_METRICS,
                    description="Positive earnings but negative sentiment",
                    suggested_strategy=ResolutionStrategy.SPECIALIST_PRIORITY,
                    metadata={
                        'eps_value': eps_value,
                        'sentiment_value': sentiment_value
                    }
                ))
        
        return conflicts
    
    def _extract_validation_conflicts(self, validation_report: CrossValidationReport) -> List[Conflict]:
        """Extract conflicts from validation report"""
        conflicts = []
        
        for issue in validation_report.issues:
            if issue.severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL]:
                conflict_type = ConflictType.MAGNITUDE_DISCREPANCY
                if 'inconsistency' in issue.issue_type:
                    conflict_type = ConflictType.LOGICAL_INCONSISTENCY
                elif 'temporal' in issue.issue_type:
                    conflict_type = ConflictType.TEMPORAL_INCONSISTENCY
                
                severity = {
                    ValidationSeverity.WARNING: 0.3,
                    ValidationSeverity.ERROR: 0.7,
                    ValidationSeverity.CRITICAL: 1.0
                }.get(issue.severity, 0.5)
                
                conflicts.append(Conflict(
                    conflict_type=conflict_type,
                    severity=severity,
                    involved_sources=issue.affected_sources,
                    conflicting_data=issue.conflicting_values,
                    metric_name=issue.issue_type,
                    category=DataCategory.FINANCIAL_METRICS,  # Default category
                    description=issue.description,
                    suggested_strategy=self.resolution_strategies.get(conflict_type, ResolutionStrategy.CONSENSUS_BUILDING),
                    metadata={'validation_issue': True, 'original_severity': issue.severity.value}
                ))
        
        return conflicts
    
    def resolve_conflicts(
        self, 
        conflicts: List[Conflict], 
        fused_data: List[FusedData],
        dynamic_weights: Dict[DataSource, float]
    ) -> Dict[str, Resolution]:
        """Resolve detected conflicts and return resolutions"""
        resolutions = {}
        
        for conflict in conflicts:
            resolution = self._resolve_single_conflict(conflict, fused_data, dynamic_weights)
            if resolution:
                resolutions[f"{conflict.metric_name}_{conflict.conflict_type.value}"] = resolution
        
        return resolutions
    
    def _resolve_single_conflict(
        self, 
        conflict: Conflict, 
        fused_data: List[FusedData],
        dynamic_weights: Dict[DataSource, float]
    ) -> Optional[Resolution]:
        """Resolve a single conflict using appropriate strategy"""
        strategy = conflict.suggested_strategy
        
        if strategy == ResolutionStrategy.HIGHEST_WEIGHT:
            return self._resolve_by_highest_weight(conflict, dynamic_weights)
        elif strategy == ResolutionStrategy.CONSENSUS_BUILDING:
            return self._resolve_by_consensus(conflict)
        elif strategy == ResolutionStrategy.SPECIALIST_PRIORITY:
            return self._resolve_by_specialist(conflict)
        elif strategy == ResolutionStrategy.TEMPORAL_PRIORITY:
            return self._resolve_by_temporal_priority(conflict)
        elif strategy == ResolutionStrategy.CONFIDENCE_PRIORITY:
            return self._resolve_by_confidence(conflict)
        elif strategy == ResolutionStrategy.HYBRID_APPROACH:
            return self._resolve_by_hybrid_approach(conflict, dynamic_weights)
        
        return None
    
    def _resolve_by_highest_weight(
        self, 
        conflict: Conflict, 
        dynamic_weights: Dict[DataSource, float]
    ) -> Resolution:
        """Resolve conflict by using highest weighted source"""
        # Find source with highest weight among conflicting sources
        best_source = max(
            conflict.involved_sources,
            key=lambda s: dynamic_weights.get(s, 0.0)
        )
        
        resolved_value = conflict.conflicting_data[best_source]
        confidence = dynamic_weights.get(best_source, 0.5)
        
        return Resolution(
            resolved_value=resolved_value,
            confidence_score=confidence,
            strategy_used=ResolutionStrategy.HIGHEST_WEIGHT,
            contributing_sources=[best_source],
            excluded_sources=[s for s in conflict.involved_sources if s != best_source],
            reasoning=f"Selected {best_source.value} due to highest weight ({confidence:.2f})",
            metadata={'selected_weight': confidence}
        )
    
    def _resolve_by_consensus(self, conflict: Conflict) -> Resolution:
        """Resolve conflict by finding consensus/average"""
        numeric_values = [
            v for v in conflict.conflicting_data.values()
            if isinstance(v, (int, float))
        ]
        
        if numeric_values:
            # Use median for robustness against outliers
            sorted_values = sorted(numeric_values)
            n = len(sorted_values)
            if n % 2 == 0:
                resolved_value = (sorted_values[n//2 - 1] + sorted_values[n//2]) / 2
            else:
                resolved_value = sorted_values[n//2]
            
            # Calculate confidence based on agreement
            mean_val = sum(numeric_values) / len(numeric_values)
            variance = sum((v - mean_val) ** 2 for v in numeric_values) / len(numeric_values)
            confidence = max(0.1, 1.0 - (variance ** 0.5) / abs(mean_val) if mean_val != 0 else 0.5)
            
            return Resolution(
                resolved_value=resolved_value,
                confidence_score=confidence,
                strategy_used=ResolutionStrategy.CONSENSUS_BUILDING,
                contributing_sources=conflict.involved_sources,
                excluded_sources=[],
                reasoning=f"Used median of {len(numeric_values)} values for consensus",
                metadata={'median_value': resolved_value, 'variance': variance}
            )
        
        # For non-numeric values, use most common
        values = list(conflict.conflicting_data.values())
        most_common = max(set(values), key=values.count)
        confidence = values.count(most_common) / len(values)
        
        return Resolution(
            resolved_value=most_common,
            confidence_score=confidence,
            strategy_used=ResolutionStrategy.CONSENSUS_BUILDING,
            contributing_sources=conflict.involved_sources,
            excluded_sources=[],
            reasoning=f"Used most common value ({confidence:.1%} agreement)",
            metadata={'agreement_ratio': confidence}
        )
    
    def _resolve_by_specialist(self, conflict: Conflict) -> Resolution:
        """Resolve conflict by deferring to domain specialist"""
        specialists = self.specialist_domains.get(conflict.category, [])
        
        # Find specialist source among conflicting sources
        specialist_sources = [s for s in conflict.involved_sources if s in specialists]
        
        if specialist_sources:
            # Use highest priority specialist
            best_specialist = specialist_sources[0]  # First in list = highest priority
            resolved_value = conflict.conflicting_data[best_specialist]
            
            return Resolution(
                resolved_value=resolved_value,
                confidence_score=0.8,  # High confidence in specialist
                strategy_used=ResolutionStrategy.SPECIALIST_PRIORITY,
                contributing_sources=[best_specialist],
                excluded_sources=[s for s in conflict.involved_sources if s != best_specialist],
                reasoning=f"Deferred to domain specialist: {best_specialist.value}",
                metadata={'specialist_category': conflict.category.value}
            )
        
        # Fallback to highest weight if no specialist available
        return self._resolve_by_highest_weight(conflict, {s: 0.5 for s in conflict.involved_sources})
    
    def _resolve_by_confidence(self, conflict: Conflict) -> Resolution:
        """Resolve conflict by using highest confidence source"""
        # This would require individual confidence scores
        # For now, use a simple heuristic based on source reliability
        source_reliability = {
            DataSource.FINANCIAL_REPORTS_ANALYST: 0.95,
            DataSource.EARNINGS_SPECIALIST_AGENT: 0.90,
            DataSource.VOLUME_ANALYSIS_AGENT: 0.85,
            DataSource.MARKET_ANALYST: 0.80,
            DataSource.FUNDAMENTALS_ANALYST: 0.75,
            DataSource.NEWS_ANALYST: 0.70,
            DataSource.SOCIAL_ANALYST: 0.60
        }
        
        best_source = max(
            conflict.involved_sources,
            key=lambda s: source_reliability.get(s, 0.5)
        )
        
        resolved_value = conflict.conflicting_data[best_source]
        confidence = source_reliability.get(best_source, 0.5)
        
        return Resolution(
            resolved_value=resolved_value,
            confidence_score=confidence,
            strategy_used=ResolutionStrategy.CONFIDENCE_PRIORITY,
            contributing_sources=[best_source],
            excluded_sources=[s for s in conflict.involved_sources if s != best_source],
            reasoning=f"Selected {best_source.value} due to highest reliability ({confidence:.2f})",
            metadata={'reliability_score': confidence}
        )
    
    def _resolve_by_hybrid_approach(
        self, 
        conflict: Conflict, 
        dynamic_weights: Dict[DataSource, float]
    ) -> Resolution:
        """Resolve conflict using hybrid approach combining multiple strategies"""
        # For signal contradictions, try to find middle ground
        if conflict.conflict_type == ConflictType.SIGNAL_CONTRADICTION:
            # Weight the signals by source reliability
            weighted_signal = 0.0
            total_weight = 0.0
            
            for source, signal in conflict.conflicting_data.items():
                if isinstance(signal, (int, float)):
                    weight = dynamic_weights.get(source, 0.1)
                    weighted_signal += signal * weight
                    total_weight += weight
            
            if total_weight > 0:
                resolved_signal = weighted_signal / total_weight
                confidence = min(0.8, total_weight)
                
                return Resolution(
                    resolved_value=resolved_signal,
                    confidence_score=confidence,
                    strategy_used=ResolutionStrategy.HYBRID_APPROACH,
                    contributing_sources=conflict.involved_sources,
                    excluded_sources=[],
                    reasoning=f"Weighted average of conflicting signals: {resolved_signal:.2f}",
                    metadata={'weighted_signal': resolved_signal, 'total_weight': total_weight}
                )
        
        # For other conflicts, combine specialist and consensus approaches
        specialists = self.specialist_domains.get(conflict.category, [])
        specialist_sources = [s for s in conflict.involved_sources if s in specialists]
        
        if specialist_sources:
            # Weight specialist opinion more heavily
            specialist_resolution = self._resolve_by_specialist(conflict)
            consensus_resolution = self._resolve_by_consensus(conflict)
            
            # Combine with 70% specialist, 30% consensus
            if isinstance(specialist_resolution.resolved_value, (int, float)) and \
               isinstance(consensus_resolution.resolved_value, (int, float)):
                resolved_value = (0.7 * specialist_resolution.resolved_value + 
                                0.3 * consensus_resolution.resolved_value)
                confidence = (0.7 * specialist_resolution.confidence_score + 
                            0.3 * consensus_resolution.confidence_score)
            else:
                resolved_value = specialist_resolution.resolved_value
                confidence = specialist_resolution.confidence_score
            
            return Resolution(
                resolved_value=resolved_value,
                confidence_score=confidence,
                strategy_used=ResolutionStrategy.HYBRID_APPROACH,
                contributing_sources=conflict.involved_sources,
                excluded_sources=[],
                reasoning="Combined specialist opinion (70%) with consensus (30%)",
                metadata={'specialist_weight': 0.7, 'consensus_weight': 0.3}
            )
        
        # Fallback to consensus if no specialist
        return self._resolve_by_consensus(conflict)
    
    def _convert_to_signal(self, value: Any, metric_name: str) -> Optional[float]:
        """Convert value to trading signal (-1 to 1)"""
        if isinstance(value, (int, float)):
            if 'sentiment' in metric_name.lower():
                return max(-1.0, min(1.0, value))
            elif 'eps' in metric_name.lower():
                return max(-1.0, min(1.0, value / 10))  # Normalize EPS
        elif isinstance(value, str):
            sentiment_map = {
                'positive': 0.5, 'bullish': 0.7, 'buy': 0.8,
                'negative': -0.5, 'bearish': -0.7, 'sell': -0.8,
                'neutral': 0.0, 'hold': 0.0
            }
            return sentiment_map.get(value.lower())
        
        return None
