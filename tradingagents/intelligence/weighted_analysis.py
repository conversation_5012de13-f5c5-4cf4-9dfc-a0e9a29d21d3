"""
Weighted Analysis System for TradingAgents Intelligence
Implements dynamic scoring and weighting based on data quality and reliability.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import math
from datetime import datetime, timedelta

from .data_fusion import DataPoint, FusedData, DataSource, DataCategory
from .cross_validation import CrossValidationReport, ValidationResult, ValidationSeverity


class QualityMetric(Enum):
    """Different quality metrics for evaluation"""
    DATA_FRESHNESS = "data_freshness"
    SOURCE_RELIABILITY = "source_reliability"
    CROSS_VALIDATION_SCORE = "cross_validation_score"
    HISTORICAL_ACCURACY = "historical_accuracy"
    DATA_COMPLETENESS = "data_completeness"
    CONSISTENCY_SCORE = "consistency_score"


@dataclass
class QualityScore:
    """Represents quality score for a data source or metric"""
    overall_score: float  # 0.0 to 1.0
    component_scores: Dict[QualityMetric, float]
    confidence_level: float
    last_updated: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class WeightedAnalysis:
    """Result of weighted analysis combining multiple sources"""
    final_recommendation: str  # BUY, SELL, HOLD
    confidence_score: float  # 0.0 to 1.0
    weighted_signals: Dict[DataSource, float]  # -1.0 to 1.0 (bearish to bullish)
    quality_scores: Dict[DataSource, QualityScore]
    contributing_factors: List[str]
    risk_factors: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)


class WeightedAnalysisEngine:
    """Main engine for weighted analysis and scoring"""
    
    def __init__(self):
        """Initialize the weighted analysis engine"""
        self.base_weights = self._initialize_base_weights()
        self.quality_evaluators = self._initialize_quality_evaluators()
        self.historical_performance = {}  # Track historical accuracy
        self.decay_factor = 0.95  # For time-based weight decay
    
    def _initialize_base_weights(self) -> Dict[DataSource, float]:
        """Initialize base weights for each data source"""
        return {
            # Custom API-powered agents - higher base weights
            DataSource.FINANCIAL_REPORTS_ANALYST: 0.25,    # 25% - Highest weight
            DataSource.EARNINGS_SPECIALIST_AGENT: 0.20,     # 20% - Very high weight
            DataSource.VOLUME_ANALYSIS_AGENT: 0.15,         # 15% - High weight
            
            # Enhanced standard agents
            DataSource.MARKET_ANALYST: 0.15,                # 15% - High weight
            DataSource.FUNDAMENTALS_ANALYST: 0.12,          # 12% - Good weight
            
            # Standard agents
            DataSource.NEWS_ANALYST: 0.08,                  # 8% - Moderate weight
            DataSource.SOCIAL_ANALYST: 0.05,                # 5% - Lower weight
        }
    
    def _initialize_quality_evaluators(self) -> Dict[QualityMetric, callable]:
        """Initialize quality evaluation functions"""
        return {
            QualityMetric.DATA_FRESHNESS: self._evaluate_data_freshness,
            QualityMetric.SOURCE_RELIABILITY: self._evaluate_source_reliability,
            QualityMetric.CROSS_VALIDATION_SCORE: self._evaluate_cross_validation,
            QualityMetric.HISTORICAL_ACCURACY: self._evaluate_historical_accuracy,
            QualityMetric.DATA_COMPLETENESS: self._evaluate_data_completeness,
            QualityMetric.CONSISTENCY_SCORE: self._evaluate_consistency
        }
    
    def calculate_dynamic_weights(
        self, 
        data_points: List[DataPoint], 
        validation_report: CrossValidationReport
    ) -> Dict[DataSource, float]:
        """Calculate dynamic weights based on current data quality"""
        
        # Start with base weights
        dynamic_weights = self.base_weights.copy()
        
        # Calculate quality scores for each source
        quality_scores = {}
        for source in DataSource:
            source_points = [p for p in data_points if p.source == source]
            if source_points:
                quality_scores[source] = self._calculate_quality_score(
                    source, source_points, validation_report
                )
        
        # Adjust weights based on quality scores
        for source, quality_score in quality_scores.items():
            if source in dynamic_weights:
                # Apply quality multiplier (0.5 to 1.5 range)
                quality_multiplier = 0.5 + quality_score.overall_score
                dynamic_weights[source] *= quality_multiplier
        
        # Normalize weights to sum to 1.0
        total_weight = sum(dynamic_weights.values())
        if total_weight > 0:
            dynamic_weights = {k: v / total_weight for k, v in dynamic_weights.items()}
        
        return dynamic_weights
    
    def _calculate_quality_score(
        self, 
        source: DataSource, 
        data_points: List[DataPoint], 
        validation_report: CrossValidationReport
    ) -> QualityScore:
        """Calculate comprehensive quality score for a data source"""
        
        component_scores = {}
        
        # Evaluate each quality metric
        for metric, evaluator in self.quality_evaluators.items():
            try:
                score = evaluator(source, data_points, validation_report)
                component_scores[metric] = max(0.0, min(1.0, score))
            except Exception as e:
                # Default to neutral score if evaluation fails
                component_scores[metric] = 0.5
        
        # Calculate weighted overall score
        metric_weights = {
            QualityMetric.DATA_FRESHNESS: 0.15,
            QualityMetric.SOURCE_RELIABILITY: 0.25,
            QualityMetric.CROSS_VALIDATION_SCORE: 0.20,
            QualityMetric.HISTORICAL_ACCURACY: 0.20,
            QualityMetric.DATA_COMPLETENESS: 0.10,
            QualityMetric.CONSISTENCY_SCORE: 0.10
        }
        
        overall_score = sum(
            component_scores[metric] * weight 
            for metric, weight in metric_weights.items()
        )
        
        # Calculate confidence level based on score variance
        scores = list(component_scores.values())
        variance = sum((s - overall_score) ** 2 for s in scores) / len(scores)
        confidence_level = max(0.0, 1.0 - variance)
        
        return QualityScore(
            overall_score=overall_score,
            component_scores=component_scores,
            confidence_level=confidence_level,
            last_updated=datetime.now(),
            metadata={'source': source.value, 'num_data_points': len(data_points)}
        )
    
    def _evaluate_data_freshness(
        self, 
        source: DataSource, 
        data_points: List[DataPoint], 
        validation_report: CrossValidationReport
    ) -> float:
        """Evaluate data freshness (more recent = higher score)"""
        if not data_points:
            return 0.0
        
        now = datetime.now()
        timestamps = [p.timestamp for p in data_points if p.timestamp]
        
        if not timestamps:
            return 0.5  # Neutral if no timestamps
        
        # Calculate average age in hours
        avg_age_hours = sum(
            (now - ts).total_seconds() / 3600 for ts in timestamps
        ) / len(timestamps)
        
        # Score decreases exponentially with age
        # Fresh data (< 1 hour) = 1.0, 24 hours = 0.5, 168 hours (1 week) = 0.1
        freshness_score = math.exp(-avg_age_hours / 24)
        
        return min(1.0, freshness_score)
    
    def _evaluate_source_reliability(
        self, 
        source: DataSource, 
        data_points: List[DataPoint], 
        validation_report: CrossValidationReport
    ) -> float:
        """Evaluate inherent source reliability"""
        # Base reliability scores
        reliability_scores = {
            DataSource.FINANCIAL_REPORTS_ANALYST: 0.95,
            DataSource.EARNINGS_SPECIALIST_AGENT: 0.90,
            DataSource.VOLUME_ANALYSIS_AGENT: 0.85,
            DataSource.MARKET_ANALYST: 0.80,
            DataSource.FUNDAMENTALS_ANALYST: 0.75,
            DataSource.NEWS_ANALYST: 0.70,
            DataSource.SOCIAL_ANALYST: 0.60
        }
        
        base_score = reliability_scores.get(source, 0.5)
        
        # Adjust based on data point confidence
        if data_points:
            avg_confidence = sum(p.confidence for p in data_points) / len(data_points)
            adjusted_score = (base_score + avg_confidence) / 2
        else:
            adjusted_score = base_score
        
        return adjusted_score
    
    def _evaluate_cross_validation(
        self, 
        source: DataSource, 
        data_points: List[DataPoint], 
        validation_report: CrossValidationReport
    ) -> float:
        """Evaluate cross-validation performance"""
        if not validation_report:
            return 0.5
        
        # Check how many validation issues involve this source
        source_issues = [
            issue for issue in validation_report.issues 
            if source in issue.affected_sources
        ]
        
        if not source_issues:
            return 1.0  # No issues = perfect score
        
        # Calculate penalty based on issue severity
        severity_penalties = {
            ValidationSeverity.INFO: 0.05,
            ValidationSeverity.WARNING: 0.15,
            ValidationSeverity.ERROR: 0.30,
            ValidationSeverity.CRITICAL: 0.50
        }
        
        total_penalty = sum(
            severity_penalties.get(issue.severity, 0.15) 
            for issue in source_issues
        )
        
        return max(0.0, 1.0 - total_penalty)
    
    def _evaluate_historical_accuracy(
        self, 
        source: DataSource, 
        data_points: List[DataPoint], 
        validation_report: CrossValidationReport
    ) -> float:
        """Evaluate historical accuracy of the source"""
        # Get historical performance for this source
        if source.value not in self.historical_performance:
            return 0.7  # Default score for new sources
        
        history = self.historical_performance[source.value]
        
        # Calculate weighted average of recent performance
        # More recent performance has higher weight
        total_weight = 0
        weighted_sum = 0
        
        for i, (timestamp, accuracy) in enumerate(history[-10:]):  # Last 10 records
            age_days = (datetime.now() - timestamp).days
            weight = self.decay_factor ** age_days
            weighted_sum += accuracy * weight
            total_weight += weight
        
        if total_weight > 0:
            return weighted_sum / total_weight
        else:
            return 0.7
    
    def _evaluate_data_completeness(
        self, 
        source: DataSource, 
        data_points: List[DataPoint], 
        validation_report: CrossValidationReport
    ) -> float:
        """Evaluate completeness of data from source"""
        if not data_points:
            return 0.0
        
        # Expected data categories for each source
        expected_categories = {
            DataSource.MARKET_ANALYST: 3,  # Price, volume, technical indicators
            DataSource.FUNDAMENTALS_ANALYST: 2,  # Financial metrics, ratios
            DataSource.NEWS_ANALYST: 2,  # News, sentiment
            DataSource.SOCIAL_ANALYST: 1,  # Sentiment
            DataSource.FINANCIAL_REPORTS_ANALYST: 3,  # Metrics, ratios, earnings
            DataSource.VOLUME_ANALYSIS_AGENT: 2,  # Volume, technical indicators
            DataSource.EARNINGS_SPECIALIST_AGENT: 2  # Earnings, metrics
        }
        
        # Count unique categories provided
        provided_categories = len(set(p.category for p in data_points))
        expected_count = expected_categories.get(source, 1)
        
        completeness_score = min(1.0, provided_categories / expected_count)
        
        # Bonus for having more data points
        data_point_bonus = min(0.2, len(data_points) * 0.02)
        
        return min(1.0, completeness_score + data_point_bonus)
    
    def _evaluate_consistency(
        self, 
        source: DataSource, 
        data_points: List[DataPoint], 
        validation_report: CrossValidationReport
    ) -> float:
        """Evaluate internal consistency of data from source"""
        if len(data_points) < 2:
            return 1.0  # Single data point is perfectly consistent
        
        # Group by category and check for consistency within each category
        category_groups = {}
        for point in data_points:
            if point.category not in category_groups:
                category_groups[point.category] = []
            category_groups[point.category].append(point)
        
        consistency_scores = []
        
        for category, points in category_groups.items():
            if len(points) > 1:
                # Check for consistency within category
                numeric_points = [p for p in points if isinstance(p.value, (int, float))]
                if len(numeric_points) > 1:
                    values = [p.value for p in numeric_points]
                    mean_val = sum(values) / len(values)
                    if mean_val != 0:
                        cv = (sum((v - mean_val) ** 2 for v in values) / len(values)) ** 0.5 / abs(mean_val)
                        consistency_score = max(0.0, 1.0 - cv)
                    else:
                        consistency_score = 1.0 if all(v == 0 for v in values) else 0.5
                    consistency_scores.append(consistency_score)
        
        if consistency_scores:
            return sum(consistency_scores) / len(consistency_scores)
        else:
            return 1.0  # No numeric data to check consistency
    
    def perform_weighted_analysis(
        self, 
        fused_data: List[FusedData], 
        validation_report: CrossValidationReport,
        dynamic_weights: Dict[DataSource, float]
    ) -> WeightedAnalysis:
        """Perform comprehensive weighted analysis"""
        
        # Extract signals from each source
        source_signals = self._extract_source_signals(fused_data)
        
        # Calculate weighted final signal
        weighted_signal = self._calculate_weighted_signal(source_signals, dynamic_weights)
        
        # Generate recommendation
        recommendation = self._generate_recommendation(weighted_signal, validation_report)
        
        # Calculate overall confidence
        confidence_score = self._calculate_overall_confidence(
            fused_data, validation_report, dynamic_weights
        )
        
        # Identify contributing and risk factors
        contributing_factors = self._identify_contributing_factors(fused_data, source_signals)
        risk_factors = self._identify_risk_factors(validation_report, fused_data)
        
        # Calculate quality scores for reporting
        quality_scores = {}
        for source in DataSource:
            if source in dynamic_weights:
                # Create mock quality score for reporting
                quality_scores[source] = QualityScore(
                    overall_score=dynamic_weights[source] / self.base_weights.get(source, 0.1),
                    component_scores={},
                    confidence_level=0.8,
                    last_updated=datetime.now()
                )
        
        return WeightedAnalysis(
            final_recommendation=recommendation,
            confidence_score=confidence_score,
            weighted_signals=source_signals,
            quality_scores=quality_scores,
            contributing_factors=contributing_factors,
            risk_factors=risk_factors,
            metadata={
                'total_sources': len(source_signals),
                'validation_score': validation_report.consistency_score if validation_report else 0.5,
                'weighted_signal': weighted_signal
            }
        )
    
    def _extract_source_signals(self, fused_data: List[FusedData]) -> Dict[DataSource, float]:
        """Extract trading signals from each source (-1.0 to 1.0)"""
        source_signals = {}
        
        # Initialize all sources with neutral signal
        for source in DataSource:
            source_signals[source] = 0.0
        
        # Extract signals from fused data
        for data in fused_data:
            for source, value in data.source_values.items():
                signal = self._convert_to_signal(data.metric_name, value, data.category)
                # Weight by consensus level
                weighted_signal = signal * data.consensus_level
                source_signals[source] += weighted_signal
        
        # Normalize signals to -1.0 to 1.0 range
        for source in source_signals:
            source_signals[source] = max(-1.0, min(1.0, source_signals[source]))
        
        return source_signals
    
    def _convert_to_signal(self, metric_name: str, value: Any, category: DataCategory) -> float:
        """Convert metric value to trading signal (-1.0 to 1.0)"""
        if not isinstance(value, (int, float)):
            if isinstance(value, str):
                sentiment_map = {
                    'positive': 0.5, 'bullish': 0.7, 'buy': 0.8,
                    'negative': -0.5, 'bearish': -0.7, 'sell': -0.8,
                    'neutral': 0.0, 'hold': 0.0
                }
                return sentiment_map.get(value.lower(), 0.0)
            return 0.0
        
        # Convert based on metric type
        if 'sentiment' in metric_name.lower():
            return max(-1.0, min(1.0, value))
        elif 'volume' in metric_name.lower():
            # High volume can be bullish or bearish depending on context
            # For now, treat as neutral
            return 0.0
        elif 'eps' in metric_name.lower() or 'earnings' in metric_name.lower():
            # Positive earnings growth = bullish
            if value > 0:
                return min(0.8, value / 10)  # Cap at 0.8
            else:
                return max(-0.8, value / 10)
        elif 'pe_ratio' in metric_name.lower():
            # Lower P/E can be bullish (undervalued)
            if 5 <= value <= 15:
                return 0.3  # Reasonable P/E = slightly bullish
            elif value > 25:
                return -0.3  # High P/E = slightly bearish
            else:
                return 0.0
        
        return 0.0  # Default neutral signal
    
    def _calculate_weighted_signal(
        self, 
        source_signals: Dict[DataSource, float], 
        dynamic_weights: Dict[DataSource, float]
    ) -> float:
        """Calculate final weighted signal"""
        weighted_sum = 0.0
        total_weight = 0.0
        
        for source, signal in source_signals.items():
            if source in dynamic_weights:
                weight = dynamic_weights[source]
                weighted_sum += signal * weight
                total_weight += weight
        
        if total_weight > 0:
            return weighted_sum / total_weight
        else:
            return 0.0
    
    def _generate_recommendation(self, weighted_signal: float, validation_report: CrossValidationReport) -> str:
        """Generate final recommendation based on weighted signal"""
        # Adjust thresholds based on validation confidence
        confidence_multiplier = validation_report.consistency_score if validation_report else 0.5
        
        buy_threshold = 0.3 / confidence_multiplier
        sell_threshold = -0.3 / confidence_multiplier
        
        if weighted_signal >= buy_threshold:
            return "BUY"
        elif weighted_signal <= sell_threshold:
            return "SELL"
        else:
            return "HOLD"
    
    def _calculate_overall_confidence(
        self, 
        fused_data: List[FusedData], 
        validation_report: CrossValidationReport,
        dynamic_weights: Dict[DataSource, float]
    ) -> float:
        """Calculate overall confidence in the analysis"""
        confidence_factors = []
        
        # Data quality confidence
        if fused_data:
            avg_data_confidence = sum(d.confidence_score for d in fused_data) / len(fused_data)
            confidence_factors.append(avg_data_confidence)
        
        # Validation confidence
        if validation_report:
            confidence_factors.append(validation_report.consistency_score)
        
        # Weight distribution confidence (more balanced = higher confidence)
        if dynamic_weights:
            weight_values = list(dynamic_weights.values())
            weight_entropy = -sum(w * math.log(w + 1e-10) for w in weight_values if w > 0)
            max_entropy = math.log(len(weight_values))
            weight_confidence = weight_entropy / max_entropy if max_entropy > 0 else 0
            confidence_factors.append(weight_confidence)
        
        if confidence_factors:
            return sum(confidence_factors) / len(confidence_factors)
        else:
            return 0.5
    
    def _identify_contributing_factors(
        self, 
        fused_data: List[FusedData], 
        source_signals: Dict[DataSource, float]
    ) -> List[str]:
        """Identify key contributing factors to the recommendation"""
        factors = []
        
        # Strong positive signals
        strong_bullish = [
            source.value for source, signal in source_signals.items() 
            if signal > 0.5
        ]
        if strong_bullish:
            factors.append(f"Strong bullish signals from: {', '.join(strong_bullish)}")
        
        # Strong negative signals
        strong_bearish = [
            source.value for source, signal in source_signals.items() 
            if signal < -0.5
        ]
        if strong_bearish:
            factors.append(f"Strong bearish signals from: {', '.join(strong_bearish)}")
        
        # High consensus data
        high_consensus = [
            data.metric_name for data in fused_data 
            if data.consensus_level > 0.8 and data.confidence_score > 0.7
        ]
        if high_consensus:
            factors.append(f"High consensus on: {', '.join(high_consensus)}")
        
        return factors
    
    def _identify_risk_factors(
        self, 
        validation_report: CrossValidationReport, 
        fused_data: List[FusedData]
    ) -> List[str]:
        """Identify risk factors that could affect the analysis"""
        risks = []
        
        if validation_report:
            # Validation issues
            critical_issues = [
                issue for issue in validation_report.issues 
                if issue.severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL]
            ]
            if critical_issues:
                risks.append(f"Data quality issues detected: {len(critical_issues)} critical problems")
            
            # Low consistency
            if validation_report.consistency_score < 0.6:
                risks.append("Low data consistency across sources")
        
        # Low consensus data
        low_consensus = [
            data.metric_name for data in fused_data 
            if data.consensus_level < 0.5
        ]
        if low_consensus:
            risks.append(f"Low consensus on key metrics: {', '.join(low_consensus)}")
        
        # Limited data sources
        total_sources = len(set(
            source for data in fused_data 
            for source in data.contributing_sources
        ))
        if total_sources < 3:
            risks.append("Limited data sources available for analysis")
        
        return risks
    
    def update_historical_performance(self, source: DataSource, accuracy: float):
        """Update historical performance tracking for a source"""
        if source.value not in self.historical_performance:
            self.historical_performance[source.value] = []
        
        self.historical_performance[source.value].append((datetime.now(), accuracy))
        
        # Keep only last 50 records
        if len(self.historical_performance[source.value]) > 50:
            self.historical_performance[source.value] = self.historical_performance[source.value][-50:]
