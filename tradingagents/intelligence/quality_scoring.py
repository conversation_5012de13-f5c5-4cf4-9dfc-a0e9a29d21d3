"""
Quality Scoring System for TradingAgents Intelligence
Implements comprehensive confidence scoring and reliability assessment.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import math
from datetime import datetime, timedelta

from .data_fusion import FusedData, DataSource, DataCategory
from .cross_validation import CrossValidationReport, ValidationResult, ValidationSeverity
from .weighted_analysis import WeightedAnalysis
from .conflict_resolution import Conflict, Resolution


class QualityDimension(Enum):
    """Different dimensions of quality assessment"""
    DATA_COMPLETENESS = "data_completeness"
    SOURCE_DIVERSITY = "source_diversity"
    TEMPORAL_CONSISTENCY = "temporal_consistency"
    CROSS_VALIDATION_SCORE = "cross_validation_score"
    CONFLICT_RESOLUTION = "conflict_resolution"
    SIGNAL_STRENGTH = "signal_strength"
    CONSENSUS_LEVEL = "consensus_level"
    HISTORICAL_ACCURACY = "historical_accuracy"


class ConfidenceLevel(Enum):
    """Confidence levels for analysis"""
    VERY_HIGH = "very_high"      # 90-100%
    HIGH = "high"                # 75-89%
    MODERATE = "moderate"        # 60-74%
    LOW = "low"                  # 40-59%
    VERY_LOW = "very_low"        # 0-39%


@dataclass
class QualityMetrics:
    """Comprehensive quality metrics"""
    overall_score: float  # 0.0 to 1.0
    confidence_level: ConfidenceLevel
    dimension_scores: Dict[QualityDimension, float]
    reliability_factors: Dict[str, float]
    risk_adjustments: Dict[str, float]
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConfidenceBreakdown:
    """Detailed breakdown of confidence calculation"""
    base_confidence: float
    quality_adjustments: Dict[str, float]
    risk_penalties: Dict[str, float]
    bonus_factors: Dict[str, float]
    final_confidence: float
    explanation: List[str]


class QualityScoringEngine:
    """Main engine for quality scoring and confidence assessment"""
    
    def __init__(self):
        """Initialize the quality scoring engine"""
        self.dimension_weights = self._initialize_dimension_weights()
        self.confidence_thresholds = self._initialize_confidence_thresholds()
        self.quality_benchmarks = self._initialize_quality_benchmarks()
        self.historical_performance = {}
    
    def _initialize_dimension_weights(self) -> Dict[QualityDimension, float]:
        """Initialize weights for different quality dimensions"""
        return {
            QualityDimension.DATA_COMPLETENESS: 0.15,
            QualityDimension.SOURCE_DIVERSITY: 0.12,
            QualityDimension.TEMPORAL_CONSISTENCY: 0.10,
            QualityDimension.CROSS_VALIDATION_SCORE: 0.20,
            QualityDimension.CONFLICT_RESOLUTION: 0.15,
            QualityDimension.SIGNAL_STRENGTH: 0.12,
            QualityDimension.CONSENSUS_LEVEL: 0.10,
            QualityDimension.HISTORICAL_ACCURACY: 0.06
        }
    
    def _initialize_confidence_thresholds(self) -> Dict[ConfidenceLevel, Tuple[float, float]]:
        """Initialize confidence level thresholds"""
        return {
            ConfidenceLevel.VERY_HIGH: (0.90, 1.00),
            ConfidenceLevel.HIGH: (0.75, 0.89),
            ConfidenceLevel.MODERATE: (0.60, 0.74),
            ConfidenceLevel.LOW: (0.40, 0.59),
            ConfidenceLevel.VERY_LOW: (0.00, 0.39)
        }
    
    def _initialize_quality_benchmarks(self) -> Dict[str, Dict[str, float]]:
        """Initialize quality benchmarks for different scenarios"""
        return {
            'excellent': {
                'min_sources': 5,
                'min_consensus': 0.8,
                'max_conflicts': 1,
                'min_validation_score': 0.9
            },
            'good': {
                'min_sources': 4,
                'min_consensus': 0.7,
                'max_conflicts': 2,
                'min_validation_score': 0.8
            },
            'acceptable': {
                'min_sources': 3,
                'min_consensus': 0.6,
                'max_conflicts': 3,
                'min_validation_score': 0.6
            },
            'poor': {
                'min_sources': 2,
                'min_consensus': 0.4,
                'max_conflicts': 5,
                'min_validation_score': 0.4
            }
        }
    
    def calculate_quality_metrics(
        self,
        fused_data: List[FusedData],
        validation_report: CrossValidationReport,
        weighted_analysis: WeightedAnalysis,
        conflicts: List[Conflict],
        resolutions: Dict[str, Resolution]
    ) -> QualityMetrics:
        """Calculate comprehensive quality metrics"""
        
        # Calculate scores for each quality dimension
        dimension_scores = {}
        
        dimension_scores[QualityDimension.DATA_COMPLETENESS] = self._score_data_completeness(fused_data)
        dimension_scores[QualityDimension.SOURCE_DIVERSITY] = self._score_source_diversity(fused_data, weighted_analysis)
        dimension_scores[QualityDimension.TEMPORAL_CONSISTENCY] = self._score_temporal_consistency(fused_data)
        dimension_scores[QualityDimension.CROSS_VALIDATION_SCORE] = self._score_cross_validation(validation_report)
        dimension_scores[QualityDimension.CONFLICT_RESOLUTION] = self._score_conflict_resolution(conflicts, resolutions)
        dimension_scores[QualityDimension.SIGNAL_STRENGTH] = self._score_signal_strength(weighted_analysis)
        dimension_scores[QualityDimension.CONSENSUS_LEVEL] = self._score_consensus_level(fused_data)
        dimension_scores[QualityDimension.HISTORICAL_ACCURACY] = self._score_historical_accuracy(weighted_analysis)
        
        # Calculate weighted overall score
        overall_score = sum(
            score * self.dimension_weights[dimension]
            for dimension, score in dimension_scores.items()
        )
        
        # Determine confidence level
        confidence_level = self._determine_confidence_level(overall_score)
        
        # Calculate reliability factors
        reliability_factors = self._calculate_reliability_factors(
            fused_data, validation_report, weighted_analysis
        )
        
        # Calculate risk adjustments
        risk_adjustments = self._calculate_risk_adjustments(
            conflicts, validation_report, weighted_analysis
        )
        
        return QualityMetrics(
            overall_score=overall_score,
            confidence_level=confidence_level,
            dimension_scores=dimension_scores,
            reliability_factors=reliability_factors,
            risk_adjustments=risk_adjustments,
            metadata={
                'calculation_timestamp': datetime.now().isoformat(),
                'total_data_points': len(fused_data),
                'sources_count': len(set(s for d in fused_data for s in d.contributing_sources)),
                'conflicts_count': len(conflicts),
                'resolutions_count': len(resolutions)
            }
        )
    
    def _score_data_completeness(self, fused_data: List[FusedData]) -> float:
        """Score data completeness across different categories"""
        if not fused_data:
            return 0.0
        
        # Expected data categories for comprehensive analysis
        expected_categories = {
            DataCategory.PRICE_DATA,
            DataCategory.VOLUME_DATA,
            DataCategory.FINANCIAL_METRICS,
            DataCategory.EARNINGS_DATA,
            DataCategory.SENTIMENT_DATA,
            DataCategory.FUNDAMENTAL_RATIOS
        }
        
        # Count available categories
        available_categories = set(d.category for d in fused_data)
        completeness_ratio = len(available_categories & expected_categories) / len(expected_categories)
        
        # Bonus for having more data points per category
        avg_points_per_category = len(fused_data) / len(available_categories) if available_categories else 0
        data_density_bonus = min(0.2, avg_points_per_category * 0.05)
        
        return min(1.0, completeness_ratio + data_density_bonus)
    
    def _score_source_diversity(self, fused_data: List[FusedData], weighted_analysis: WeightedAnalysis) -> float:
        """Score diversity of data sources"""
        if not fused_data:
            return 0.0
        
        # Count unique sources
        all_sources = set(s for d in fused_data for s in d.contributing_sources)
        source_count = len(all_sources)
        
        # Maximum expected sources
        max_sources = len(DataSource)
        diversity_ratio = source_count / max_sources
        
        # Check for balanced contribution (no single source dominance)
        source_weights = weighted_analysis.weighted_signals
        if source_weights:
            max_weight = max(source_weights.values())
            balance_score = 1.0 - max_weight if max_weight < 1.0 else 0.0
        else:
            balance_score = 0.5
        
        # Combine diversity and balance
        return (diversity_ratio * 0.7 + balance_score * 0.3)
    
    def _score_temporal_consistency(self, fused_data: List[FusedData]) -> float:
        """Score temporal consistency of data"""
        if not fused_data:
            return 0.0
        
        # This would require timestamp information in fused data
        # For now, return a default score based on data availability
        return 0.8  # Assume good temporal consistency
    
    def _score_cross_validation(self, validation_report: CrossValidationReport) -> float:
        """Score cross-validation results"""
        if not validation_report:
            return 0.5  # Neutral score if no validation
        
        # Base score from consistency
        base_score = validation_report.consistency_score
        
        # Adjust based on validation result
        result_adjustments = {
            ValidationResult.CONSISTENT: 0.0,
            ValidationResult.MINOR_DISCREPANCY: -0.1,
            ValidationResult.MAJOR_CONFLICT: -0.3,
            ValidationResult.INSUFFICIENT_DATA: -0.2
        }
        
        adjustment = result_adjustments.get(validation_report.overall_result, 0.0)
        
        return max(0.0, min(1.0, base_score + adjustment))
    
    def _score_conflict_resolution(self, conflicts: List[Conflict], resolutions: Dict[str, Resolution]) -> float:
        """Score conflict detection and resolution effectiveness"""
        if not conflicts:
            return 1.0  # Perfect score if no conflicts
        
        # Resolution rate
        resolution_rate = len(resolutions) / len(conflicts) if conflicts else 1.0
        
        # Quality of resolutions (based on confidence scores)
        if resolutions:
            avg_resolution_confidence = sum(r.confidence_score for r in resolutions.values()) / len(resolutions)
        else:
            avg_resolution_confidence = 0.0
        
        # Severity penalty (more severe conflicts = lower score)
        severity_weights = {'low': 0.1, 'medium': 0.2, 'high': 0.4, 'critical': 0.6}
        total_severity = sum(severity_weights.get(c.severity, 0.3) for c in conflicts)
        severity_penalty = min(0.5, total_severity / len(conflicts))
        
        base_score = resolution_rate * 0.6 + avg_resolution_confidence * 0.4
        final_score = max(0.0, base_score - severity_penalty)
        
        return final_score
    
    def _score_signal_strength(self, weighted_analysis: WeightedAnalysis) -> float:
        """Score strength and clarity of trading signals"""
        if not weighted_analysis.weighted_signals:
            return 0.0
        
        # Calculate signal clarity (how far from neutral)
        signals = list(weighted_analysis.weighted_signals.values())
        avg_signal_strength = sum(abs(s) for s in signals) / len(signals)
        
        # Bonus for consistent signal direction
        positive_signals = sum(1 for s in signals if s > 0.1)
        negative_signals = sum(1 for s in signals if s < -0.1)
        neutral_signals = len(signals) - positive_signals - negative_signals
        
        # Consistency bonus (more signals in same direction = higher score)
        max_directional = max(positive_signals, negative_signals)
        consistency_bonus = max_directional / len(signals) if signals else 0
        
        # Combine strength and consistency
        return min(1.0, avg_signal_strength * 0.7 + consistency_bonus * 0.3)
    
    def _score_consensus_level(self, fused_data: List[FusedData]) -> float:
        """Score consensus level across data sources"""
        if not fused_data:
            return 0.0
        
        # Average consensus level across all fused data
        consensus_scores = [d.consensus_level for d in fused_data]
        avg_consensus = sum(consensus_scores) / len(consensus_scores)
        
        # Bonus for high-confidence consensus
        high_consensus_count = sum(1 for score in consensus_scores if score > 0.8)
        high_consensus_ratio = high_consensus_count / len(consensus_scores)
        
        return avg_consensus * 0.8 + high_consensus_ratio * 0.2
    
    def _score_historical_accuracy(self, weighted_analysis: WeightedAnalysis) -> float:
        """Score based on historical accuracy of sources"""
        # This would require historical performance tracking
        # For now, return a score based on source reliability
        if not weighted_analysis.quality_scores:
            return 0.7  # Default score
        
        reliability_scores = [qs.overall_score for qs in weighted_analysis.quality_scores.values()]
        return sum(reliability_scores) / len(reliability_scores) if reliability_scores else 0.7
    
    def _determine_confidence_level(self, overall_score: float) -> ConfidenceLevel:
        """Determine confidence level based on overall score"""
        for level, (min_score, max_score) in self.confidence_thresholds.items():
            if min_score <= overall_score <= max_score:
                return level
        return ConfidenceLevel.LOW  # Default fallback
    
    def _calculate_reliability_factors(
        self,
        fused_data: List[FusedData],
        validation_report: CrossValidationReport,
        weighted_analysis: WeightedAnalysis
    ) -> Dict[str, float]:
        """Calculate various reliability factors"""
        factors = {}
        
        # Data volume factor
        factors['data_volume'] = min(1.0, len(fused_data) / 10)  # Normalize to 10 data points
        
        # Source count factor
        unique_sources = len(set(s for d in fused_data for s in d.contributing_sources))
        factors['source_count'] = min(1.0, unique_sources / 5)  # Normalize to 5 sources
        
        # Validation factor
        factors['validation_quality'] = validation_report.consistency_score if validation_report else 0.5
        
        # Weighted analysis confidence
        factors['analysis_confidence'] = weighted_analysis.confidence_score
        
        # High-confidence data ratio
        high_conf_data = sum(1 for d in fused_data if d.confidence_score > 0.8)
        factors['high_confidence_ratio'] = high_conf_data / len(fused_data) if fused_data else 0
        
        return factors
    
    def _calculate_risk_adjustments(
        self,
        conflicts: List[Conflict],
        validation_report: CrossValidationReport,
        weighted_analysis: WeightedAnalysis
    ) -> Dict[str, float]:
        """Calculate risk-based adjustments to confidence"""
        adjustments = {}
        
        # Conflict risk
        if conflicts:
            high_severity_conflicts = sum(1 for c in conflicts if c.severity > 0.7)
            adjustments['conflict_risk'] = -min(0.3, high_severity_conflicts * 0.1)
        else:
            adjustments['conflict_risk'] = 0.0
        
        # Validation risk
        if validation_report:
            critical_issues = sum(
                1 for issue in validation_report.issues 
                if issue.severity == ValidationSeverity.CRITICAL
            )
            adjustments['validation_risk'] = -min(0.2, critical_issues * 0.05)
        else:
            adjustments['validation_risk'] = -0.1  # Penalty for no validation
        
        # Low confidence risk
        if weighted_analysis.confidence_score < 0.5:
            adjustments['low_confidence_risk'] = -(0.5 - weighted_analysis.confidence_score)
        else:
            adjustments['low_confidence_risk'] = 0.0
        
        # Data sparsity risk
        contributing_sources = len(weighted_analysis.weighted_signals)
        if contributing_sources < 3:
            adjustments['data_sparsity_risk'] = -0.15
        else:
            adjustments['data_sparsity_risk'] = 0.0
        
        return adjustments
    
    def generate_confidence_breakdown(
        self,
        quality_metrics: QualityMetrics,
        weighted_analysis: WeightedAnalysis
    ) -> ConfidenceBreakdown:
        """Generate detailed confidence breakdown with explanations"""
        
        # Base confidence from weighted analysis
        base_confidence = weighted_analysis.confidence_score
        
        # Quality adjustments
        quality_adjustments = {}
        for dimension, score in quality_metrics.dimension_scores.items():
            weight = self.dimension_weights[dimension]
            adjustment = (score - 0.5) * weight * 0.5  # Scale adjustment
            quality_adjustments[dimension.value] = adjustment
        
        # Risk penalties
        risk_penalties = quality_metrics.risk_adjustments
        
        # Bonus factors
        bonus_factors = {}
        if quality_metrics.overall_score > 0.9:
            bonus_factors['excellence_bonus'] = 0.05
        if len(quality_metrics.reliability_factors) >= 4:
            bonus_factors['comprehensive_data_bonus'] = 0.03
        
        # Calculate final confidence
        total_quality_adjustment = sum(quality_adjustments.values())
        total_risk_penalty = sum(risk_penalties.values())
        total_bonus = sum(bonus_factors.values())
        
        final_confidence = max(0.0, min(1.0, 
            base_confidence + total_quality_adjustment + total_risk_penalty + total_bonus
        ))
        
        # Generate explanations
        explanations = []
        explanations.append(f"Base confidence from weighted analysis: {base_confidence:.1%}")
        
        if total_quality_adjustment > 0:
            explanations.append(f"Quality improvements: +{total_quality_adjustment:.1%}")
        elif total_quality_adjustment < 0:
            explanations.append(f"Quality concerns: {total_quality_adjustment:.1%}")
        
        if total_risk_penalty < 0:
            explanations.append(f"Risk adjustments: {total_risk_penalty:.1%}")
        
        if total_bonus > 0:
            explanations.append(f"Bonus factors: +{total_bonus:.1%}")
        
        explanations.append(f"Final confidence: {final_confidence:.1%}")
        
        return ConfidenceBreakdown(
            base_confidence=base_confidence,
            quality_adjustments=quality_adjustments,
            risk_penalties=risk_penalties,
            bonus_factors=bonus_factors,
            final_confidence=final_confidence,
            explanation=explanations
        )
    
    def assess_recommendation_reliability(
        self,
        quality_metrics: QualityMetrics,
        weighted_analysis: WeightedAnalysis
    ) -> Dict[str, Any]:
        """Assess overall reliability of the recommendation"""
        
        confidence_breakdown = self.generate_confidence_breakdown(quality_metrics, weighted_analysis)
        
        # Determine reliability category
        reliability_categories = {
            'very_high': "Recommendation backed by high-quality, consistent data from multiple sources",
            'high': "Recommendation supported by good data quality with minor inconsistencies",
            'moderate': "Recommendation based on adequate data but with some quality concerns",
            'low': "Recommendation has significant data quality issues or conflicts",
            'very_low': "Recommendation not reliable due to poor data quality or major conflicts"
        }
        
        reliability_category = quality_metrics.confidence_level.value
        reliability_description = reliability_categories[reliability_category]
        
        # Generate actionable insights
        insights = []
        
        if quality_metrics.overall_score > 0.8:
            insights.append("High-quality analysis with strong data foundation")
        elif quality_metrics.overall_score < 0.6:
            insights.append("Consider gathering additional data before acting on recommendation")
        
        if quality_metrics.dimension_scores[QualityDimension.CONSENSUS_LEVEL] < 0.6:
            insights.append("Low consensus among sources - monitor for emerging clarity")
        
        if quality_metrics.dimension_scores[QualityDimension.CONFLICT_RESOLUTION] < 0.7:
            insights.append("Unresolved conflicts present - exercise additional caution")
        
        return {
            'reliability_category': reliability_category,
            'reliability_description': reliability_description,
            'confidence_breakdown': confidence_breakdown,
            'quality_score': quality_metrics.overall_score,
            'key_insights': insights,
            'recommendation_strength': self._assess_recommendation_strength(quality_metrics, weighted_analysis)
        }
    
    def _assess_recommendation_strength(
        self,
        quality_metrics: QualityMetrics,
        weighted_analysis: WeightedAnalysis
    ) -> str:
        """Assess the strength of the recommendation"""
        
        # Combine quality score and analysis confidence
        combined_score = (quality_metrics.overall_score + weighted_analysis.confidence_score) / 2
        
        # Factor in signal strength
        signal_strength = quality_metrics.dimension_scores.get(QualityDimension.SIGNAL_STRENGTH, 0.5)
        
        # Final strength assessment
        final_strength = combined_score * 0.7 + signal_strength * 0.3
        
        if final_strength >= 0.85:
            return "Very Strong"
        elif final_strength >= 0.70:
            return "Strong"
        elif final_strength >= 0.55:
            return "Moderate"
        elif final_strength >= 0.40:
            return "Weak"
        else:
            return "Very Weak"
