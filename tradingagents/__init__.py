#!/usr/bin/env python3
"""
TradingAgents-VN Core Module

This is a multi-agent stock analysis system that supports the comprehensive analysis of Vietnam and US stocks.
"""

__version__ = "0.1.12"
__author__ = "TradingAgents-VN Team"
__description__ = "Multi-agent stock analysis system for Vietnamese markets"

# Import core modules
try:
    from .config import config_manager
    from .utils import logging_manager
except ImportError:
    # If import fails, it does not affect the basic functionality of the module
    pass

__all__ = [
    "__version__",
    "__author__", 
    "__description__"
]