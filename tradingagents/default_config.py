import os

DEFAULT_CONFIG = {
    "project_dir": os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
    "results_dir": os.getenv("TRADINGAGENTS_RESULTS_DIR", "./results"),
    "data_dir": os.getenv("TRADINGAGENTS_DATA_DIR", "./data"),
    "data_cache_dir": os.path.join(
        os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
        "dataflows/data_cache",
    ),
    # Database
    "database_url": os.getenv("DATABASE_URL", "mysql+pymysql://root:root@localhost:3306/trading_agent"),

    # LLM settings
    "llm_provider": "google",
    "deep_think_llm": os.getenv("DEEP_THINK_LLM", "gemini-1.5-flash"),
    "quick_think_llm": os.getenv("QUICK_THINK_LLM", "gemini-1.5-flash-lite"),
    "backend_url": "https://generativelanguage.googleapis.com/v1beta",
    # Debate and discussion settings
    "max_debate_rounds": 1,
    "max_risk_discuss_rounds": 1,
    "max_recur_limit": 100,

    # API Configuration - TODO: Replace with your actual API details
    # Financial Reports API
    "financial_api_base_url": os.getenv("FINANCIAL_API_BASE_URL", "https://your-financial-api.com/api/v1"),
    "financial_api_key": os.getenv("FINANCIAL_API_KEY", "YOUR_FINANCIAL_API_KEY_HERE"),

    # Trading Data API
    "trading_api_base_url": os.getenv("TRADING_API_BASE_URL", "https://your-trading-api.com/api/v1"),
    "trading_api_key": os.getenv("TRADING_API_KEY", "YOUR_TRADING_API_KEY_HERE"),

    # Macroeconomic Data API
    "macro_api_base_url": os.getenv("MACRO_API_BASE_URL", "http://api.worldbank.org/v2"),
    "macro_api_key": os.getenv("MACRO_API_KEY", ""),  # World Bank API doesn't require key

    # API Settings
    "api_timeout": 30,
    "use_custom_financial_api": os.getenv("USE_CUSTOM_FINANCIAL_API", "false").lower() == "true",
    "use_custom_trading_api": os.getenv("USE_CUSTOM_TRADING_API", "false").lower() == "true",
    "use_macro_api": os.getenv("USE_MACRO_API", "true").lower() == "true",
}
