import os

DEFAULT_CONFIG = {
    "project_dir": os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
    "results_dir": os.getenv("TRADINGAGENTS_RESULTS_DIR", "./results"),
    "data_dir": os.getenv("TRADINGAGENTS_DATA_DIR", "./data"),
    "data_cache_dir": os.path.join(
        os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
        "dataflows/data_cache",
    ),
    # LLM settings
    "llm_provider": "google",
    "deep_think_llm": os.getenv("DEEP_THINK_LLM", "gemini-2.5-flash"),
    "quick_think_llm": os.getenv("QUICK_THINK_LLM", "gemini-2.5-flash-lite"),
    "backend_url": "https://generativelanguage.googleapis.com/v1beta",
    # Debate and discussion settings
    "max_debate_rounds": 1,
    "max_risk_discuss_rounds": 1,
    "max_recur_limit": 100,

    # API Configuration - TODO: Replace with your actual API details
    # Financial Reports API
    "financial_api_base_url": os.getenv("FINANCIAL_API_BASE_URL", "https://your-financial-api.com/api/v1"),
    "financial_api_key": os.getenv("FINANCIAL_API_KEY", "YOUR_FINANCIAL_API_KEY_HERE"),

    # Trading Data API
    "trading_api_base_url": os.getenv("TRADING_API_BASE_URL", "https://your-trading-api.com/api/v1"),
    "trading_api_key": os.getenv("TRADING_API_KEY", "YOUR_TRADING_API_KEY_HERE"),

    # API Settings
    "api_timeout": 30,
    "use_custom_financial_api": os.getenv("USE_CUSTOM_FINANCIAL_API", "false").lower() == "true",
    "use_custom_trading_api": os.getenv("USE_CUSTOM_TRADING_API", "false").lower() == "true",
}
