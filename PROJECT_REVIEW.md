# Phân tích dự án TradingAgents

<PERSON><PERSON><PERSON> bạ<PERSON>, tôi đã xem qua dự án `TradingAgents` của bạn. Đ<PERSON><PERSON> là một hệ thống được thiết kế rất bài bản và thú vị, sử dụng nhiều agent AI để thực hiện phân tích và giao dịch tài chính một cách tự động.

Dưới đây là tài liệu phân tích chi tiết về cấu trúc và luồng hoạt động của dự án để bạn dễ dàng nắm bắt.

### 1. Tổng quan dự án (Project Overview)

`TradingAgents` là một framework sử dụng các agent (tác nhân) AI chuyên biệt để mô phỏng quy trình làm việc của một nhóm phân tích tài chính. Mỗi agent có một vai trò riê<PERSON>, từ thu thậ<PERSON> d<PERSON> l<PERSON>, phân tích các kh<PERSON> cạnh <PERSON>h<PERSON><PERSON> (<PERSON><PERSON>, k<PERSON>u<PERSON>, tin tứ<PERSON>), tran<PERSON> luận về rủi ro, và cuối cùng đưa ra quyết định giao dịch.

Kiến trúc này theo mô hình "multi-agent system", giúp chia nhỏ các tác vụ phức tạp và tận dụng khả năng chuyên môn của từng agent.

### 2. Cấu trúc thư mục chính

Đây là giải thích về các thư mục quan trọng nhất và vai trò của chúng:

*   `cli/`: Chứa mã nguồn cho giao diện dòng lệnh (Command-Line Interface). Đây là điểm khởi đầu (entry point) để người dùng tương tác với hệ thống. `main.py` là file chính để chạy chương trình.
*   `tradingagents/`: Thư mục cốt lõi chứa toàn bộ logic của ứng dụng.
    *   `agents/`: **Trái tim của hệ thống**. Nơi định nghĩa các loại agent khác nhau.
        *   `analysts/`: Các agent chuyên phân tích (tin tức, cơ bản, thị trường).
        *   `researchers/`: Các agent chuyên nghiên cứu theo hai hướng đối lập (`bull` - giá lên, `bear` - giá xuống) để có cái nhìn đa chiều.
        *   `risk_mgmt/`: Các agent chuyên tranh luận và đánh giá rủi ro từ các góc nhìn khác nhau (bảo thủ, trung lập, quyết đoán).
        *   `managers/`: Các agent quản lý, điều phối công việc của các agent cấp dưới.
        *   `trader/`: Agent cuối cùng, nhận tất cả thông tin đã phân tích để đưa ra quyết định giao dịch (mua/bán/giữ).
    *   `dataflows/`: Chịu trách nhiệm thu thập dữ liệu từ các nguồn bên ngoài như Yahoo Finance (`yfin_utils.py`), Google News (`googlenews_utils.py`), và các trang web khác.
    *   `graph/`: **Bộ điều phối trung tâm**. Thư mục này định nghĩa cách các agent tương tác với nhau. `trading_graph.py` có vẻ như sử dụng một cấu trúc đồ thị (graph) để quản lý luồng công việc, truyền tín hiệu và thông tin giữa các agent.
    *   `results/`: Nơi lưu trữ kết quả phân tích cho từng mã cổ phiếu.

### 3. Luồng hoạt động (Operational Flow)

Dựa trên cấu trúc file, luồng hoạt động của một phiên phân tích cổ phiếu có thể diễn ra như sau:

1.  **Khởi động (Initiation):** Người dùng chạy ứng dụng từ `cli/main.py`, cung cấp một mã cổ phiếu (ví dụ: `HPG.VN`).
2.  **Điều phối (Orchestration):** `trading_graph.py` được kích hoạt. Nó đóng vai trò như một "nhạc trưởng", điều phối toàn bộ quy trình.
3.  **Thu thập dữ liệu (Data Collection):** Graph ra lệnh cho các module trong `dataflows/` để lấy dữ liệu:
    *   `yfin_utils.py` tải dữ liệu giá, khối lượng giao dịch, và các chỉ số tài chính.
    *   `googlenews_utils.py` và `website_news_utils.py` tìm kiếm và tải các tin tức liên quan đến mã cổ phiếu.
4.  **Phân tích đa chiều (Multi-faceted Analysis):** Dữ liệu được chuyển đến các agent `analysts/`:
    *   `FundamentalsAnalyst` phân tích các chỉ số tài chính (P/E, P/B, ROE...).
    *   `NewsAnalyst` phân tích sắc thái (sentiment) và nội dung các tin tức.
    *   `MarketAnalyst` có thể phân tích các xu hướng chung của thị trường.
5.  **Nghiên cứu đối lập (Bull vs. Bear Research):** Kết quả phân tích sơ bộ được đưa cho các agent `researchers/`:
    *   `BullResearcher` xây dựng luận điểm "tại sao nên mua" cổ phiếu này.
    *   `BearResearcher` xây dựng luận điểm "tại sao nên bán/tránh xa" cổ phiếu này.
6.  **Tranh luận & Đánh giá rủi ro (Risk Debate):** Các luận điểm trái chiều từ `researchers` được đưa vào một "phòng họp" ảo, nơi các agent `risk_mgmt/` tiến hành tranh luận để đánh giá rủi ro từ nhiều góc độ (bảo thủ, quyết đoán).
7.  **Tổng hợp (Synthesis):** `ResearchManager` và `RiskManager` tổng hợp tất cả các báo cáo, tranh luận và đưa ra một bản báo cáo cuối cùng, bao gồm cả các tín hiệu giao dịch tiềm năng.
8.  **Quyết định giao dịch (Trading Decision):** Báo cáo tổng hợp được chuyển đến `trader/trader.py`. Agent này sẽ đưa ra quyết định cuối cùng: Mua, Bán, hoặc Giữ.
9.  **Lưu kết quả (Output):** Kết quả phân tích và quyết định được lưu vào thư mục `results/` và hiển thị cho người dùng qua CLI.

### 4. Sơ đồ luồng hoạt động (Flowchart)

Để trực quan hóa, đây là sơ đồ Mermaid mô tả luồng hoạt động trên. Bạn có thể copy và dán vào một trình soạn thảo Markdown có hỗ trợ Mermaid (như Github, VS Code với extension) để xem dưới dạng biểu đồ.

'''mermaid
graph TD
    subgraph "1. User Input"
        A[Người dùng qua CLI] -->|Mã cổ phiếu, ví dụ: HPG| B{Trading Graph Orchestrator};
    end

    subgraph "2. Data Collection"
        B --> C[dataflows: yfin_utils];
        B --> D[dataflows: googlenews_utils];
        B --> E[dataflows: website_news_utils];
    end

    subgraph "3. Analysis Agents"
        C -->|Dữ liệu tài chính| F[FundamentalsAnalyst];
        D -->|Tin tức| G[NewsAnalyst];
        E -->|Tin tức| G;
    end

    subgraph "4. Bull/Bear Debate"
        F --> H{Research Manager};
        G --> H;
        H --> I[Bull Researcher];
        H --> J[Bear Researcher];
    end

    subgraph "5. Risk Assessment"
        I --> K{Risk Manager};
        J --> K;
        K --> L[Conservative Debator];
        K --> M[Aggressive Debator];
    end

    subgraph "6. Final Decision"
        L --> N[Trader Agent];
        M --> N;
        N -->|Quyết định: Mua/Bán/Giữ| O[Output];
    end

    subgraph "7. Output"
        O --> P[Lưu vào thư mục results/];
        O --> Q[Hiển thị trên CLI];
    end
'''

### 5. Cách bắt đầu chạy dự án

1.  **Cài đặt các thư viện cần thiết:**
    `pip install -r requirements.txt`
2.  **Chạy ứng dụng:**
    `python3 cli/main.py`
    *(Lưu ý: Tôi đã sửa file `cli/main.py` để khắc phục lỗi `ModuleNotFoundError` ban đầu.)*
