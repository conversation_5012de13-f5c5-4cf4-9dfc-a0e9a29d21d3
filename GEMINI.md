# Project Overview

This project, **TradingAgents**, is a multi-agent financial trading framework that uses Large Language Models (LLMs) to simulate the decision-making process of a real-world trading firm. It deploys specialized agents for fundamental analysis, sentiment analysis, technical analysis, and risk management to collaboratively evaluate market conditions and inform trading decisions. The framework is built using Python and leverages the `langgraph` library for creating the agentic workflows.

**Key Technologies:**

*   **Programming Language:** Python 3.10+
*   **Core Framework:** `langgraph`
*   **LLM Integration:** `langchain-openai`, `langchain-anthropic`, `langchain-google-genai`
*   **Financial Data:** `finnhub-python`, `yfinance`, `stockstats`
*   **CLI:** `typer`, `rich`
*   **Dependencies:** `pandas`, `requests`, `praw`, `feedparser`, etc. (see `requirements.txt` or `pyproject.toml`)

**Architecture:**

The framework is designed around a graph-based architecture where different agents (nodes) perform specific tasks and pass information to each other. The main components are:

1.  **Analyst Team:** A group of specialized agents that analyze the market from different perspectives (fundamentals, sentiment, news, technicals).
2.  **Researcher Team:** Bullish and bearish researchers who debate the findings of the analyst team.
3.  **Trader Agent:** Makes trading decisions based on the analysis from the other agents.
4.  **Risk Management and Portfolio Manager:** Evaluates the risk of the proposed trades and gives the final approval.

The entire workflow is orchestrated by the `TradingAgentsGraph` class in `tradingagents/graph/trading_graph.py`.

# Building and Running

**1. Installation:**

```bash
# Clone the repository
git clone https://github.com/TauricResearch/TradingAgents.git
cd TradingAgents

# Create a virtual environment
python -m venv myenv
source myenv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

**2. API Keys:**

You need to set the following environment variables with your API keys:

```bash
export FINNHUB_API_KEY=<YOUR_FINNHUB_API_KEY>
export OPENAI_API_KEY=<YOUR_OPENAI_API_KEY>
export GEMINI_API_KEY=<YOUR_GEMINI_API_KEY>
export GOOGLE_API_KEY=<YOUR_GEMINI_API_KEY>
```

**3. Running the CLI:**

The project includes a command-line interface (CLI) for interacting with the trading agents.

```bash
python -m cli.main
```

This will launch an interactive prompt where you can configure and run the analysis.

**4. Running as a Package:**

You can also use `TradingAgents` as a Python package in your own code.

```python
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

ta = TradingAgentsGraph(debug=True, config=DEFAULT_CONFIG.copy())

# forward propagate
_, decision = ta.propagate("NVDA", "2024-05-10")
print(decision)
```

# Development Conventions

*   **Configuration:** The default configuration is stored in `tradingagents/default_config.py`. You can override these settings by creating a custom config dictionary.
*   **LLM Agnostic:** The framework is designed to be LLM-agnostic and supports models from OpenAI, Anthropic, and Google. The LLM provider can be configured in the `default_config.py` file.
*   **Modularity:** The agent-based architecture is modular, allowing for the addition of new agents or the modification of existing ones.
*   **Debugging:** The `TradingAgentsGraph` can be initialized with `debug=True` to enable detailed logging and tracing of the agent interactions.
*   **CLI:** The CLI is built with `typer` and `rich` for a user-friendly experience. The main logic for the CLI is in `cli/main.py`.
