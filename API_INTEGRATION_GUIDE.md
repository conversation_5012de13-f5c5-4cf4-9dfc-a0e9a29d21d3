# Hướng dẫn tích hợp API dữ liệu tài chính và giao dịch

## Tổng quan

Hệ thống TradingAgents đã được mở rộng để hỗ trợ tích hợp API tùy chỉnh cho:
1. **B<PERSON>o cáo tài chính** - Thay thế/bổ sung cho web search trong `get_fundamentals`
2. **Dữ liệu giao dịch** - Thay thế/bổ sung cho Yahoo Finance trong `get_YFin_data_online`

## Cấu trúc files đã tạo

```
tradingagents/
├── dataflows/
│   ├── financial_reports_api.py    # API connector cho báo cáo tài chính
│   ├── trading_data_api.py         # API connector cho dữ liệu giao dịch
│   └── interface.py                # Đã cập nhật để sử dụng API mới
├── default_config.py               # Đã thêm config cho API
└── API_INTEGRATION_GUIDE.md        # File này
```

## Cách cấu hình API của bạn

### 1. Cập nhật Environment Variables

Tạo file `.env` hoặc set environment variables:

```bash
# Financial Reports API
export FINANCIAL_API_BASE_URL="https://your-financial-api.com/api/v1"
export FINANCIAL_API_KEY="your_financial_api_key_here"
export USE_CUSTOM_FINANCIAL_API="true"

# Trading Data API  
export TRADING_API_BASE_URL="https://your-trading-api.com/api/v1"
export TRADING_API_KEY="your_trading_api_key_here"
export USE_CUSTOM_TRADING_API="true"
```

### 2. Cập nhật API Connectors

#### A. Financial Reports API (`tradingagents/dataflows/financial_reports_api.py`)

**Cần thay đổi:**

1. **Base URL và Authentication** (dòng 23-30):
```python
# TODO: Replace these with your actual API details
self.base_url = self.config.get("financial_api_base_url", "https://your-financial-api.com/api/v1")
self.api_key = self.config.get("financial_api_key", "YOUR_API_KEY_HERE")

# Setup headers
self.headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {self.api_key}",  # TODO: Adjust auth method as needed
    "User-Agent": "TradingAgents/1.0"
}
```

2. **API Endpoints** (các method get_financial_statements, get_financial_ratios, get_earnings_data):
```python
# TODO: Replace with your actual API endpoint
endpoint = f"{self.base_url}/financials/{statement_type}"
```

3. **Response Format** (method format_financial_data):
```python
# TODO: Adjust based on your API response structure
for period in data.get("periods", []):
    formatted_output += f"### Period: {period.get('date', 'N/A')}\n"
```

#### B. Trading Data API (`tradingagents/dataflows/trading_data_api.py`)

**Cần thay đổi:**

1. **Base URL và Authentication** (dòng 23-30):
```python
# TODO: Replace these with your actual API details
self.base_url = self.config.get("trading_api_base_url", "https://your-trading-api.com/api/v1")
self.api_key = self.config.get("trading_api_key", "YOUR_API_KEY_HERE")
```

2. **API Endpoints** (các method get_historical_prices, get_real_time_quote, etc.):
```python
# TODO: Replace with your actual API endpoint
endpoint = f"{self.base_url}/historical"
```

3. **Response Format** (method format_price_data):
```python
# TODO: Adjust based on your API response structure
prices = data.get("prices", [])
```

## Cách sử dụng

### 1. Chỉ sử dụng API tùy chỉnh

```python
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# Cấu hình để sử dụng API tùy chỉnh
config = DEFAULT_CONFIG.copy()
config["use_custom_financial_api"] = True
config["use_custom_trading_api"] = True

# Khởi tạo với config mới
ta = TradingAgentsGraph(debug=True, config=config)

# Sử dụng bình thường
_, decision = ta.propagate("ACB", "2024-05-10")
```

### 2. Hybrid mode (API + fallback)

```python
# Mặc định: sử dụng API trước, fallback về Yahoo Finance/web search nếu lỗi
config = DEFAULT_CONFIG.copy()
config["use_custom_financial_api"] = True  # Sử dụng API cho fundamentals
config["use_custom_trading_api"] = False   # Vẫn dùng Yahoo Finance

ta = TradingAgentsGraph(debug=True, config=config)
```

## Testing

### 1. Test API connectors riêng lẻ

```python
# Test Financial Reports API
from tradingagents.dataflows.financial_reports_api import get_financial_api

financial_api = get_financial_api()
result = financial_api.get_comprehensive_fundamentals("ACB", "2024-05-10")
print(result)

# Test Trading Data API
from tradingagents.dataflows.trading_data_api import get_trading_api

trading_api = get_trading_api()
result = trading_api.get_comprehensive_market_data("ACB", "2024-05-01", "2024-05-10")
print(result)
```

### 2. Test integration với agents

```python
# Test get_fundamentals với API mới
import tradingagents.dataflows.interface as interface

result = interface.get_fundamentals("ACB", "2024-05-10")
print(result)

# Test get_YFin_data_online với API mới
result = interface.get_YFin_data_online("ACB", "2024-05-01", "2024-05-10")
print(result)
```

## Troubleshooting

### 1. API không hoạt động
- Kiểm tra environment variables
- Kiểm tra API key và permissions
- Kiểm tra network connectivity
- Xem logs để debug

### 2. Response format không đúng
- Cập nhật các method `format_*` trong API connectors
- Kiểm tra structure của API response
- Adjust parsing logic

### 3. Fallback không hoạt động
- Kiểm tra exception handling
- Đảm bảo Yahoo Finance vẫn accessible
- Kiểm tra config flags

## Mở rộng thêm

### 1. Thêm API endpoints mới
- Thêm methods mới vào API connectors
- Cập nhật formatting methods
- Thêm vào tools nếu cần

### 2. Tạo agents mới
- Tạo specialized agents sử dụng API data
- Thêm vào graph setup
- Cập nhật tool nodes

### 3. Real-time data
- Implement WebSocket connections
- Add streaming data support
- Update memory systems

## Lưu ý quan trọng

1. **Security**: Không commit API keys vào git
2. **Rate limiting**: Implement rate limiting cho API calls
3. **Caching**: Consider caching API responses
4. **Error handling**: Robust error handling cho production
5. **Monitoring**: Add logging và monitoring cho API calls

## Liên hệ

Nếu cần hỗ trợ thêm về integration, vui lòng cung cấp:
- Sample API response format
- API documentation
- Specific error messages
- Use case requirements
